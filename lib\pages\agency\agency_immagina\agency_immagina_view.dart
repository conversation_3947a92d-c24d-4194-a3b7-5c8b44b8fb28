import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/pages/agency/agency_immagina/agency_immagina_view_controller.dart';
import 'package:newarc_platform/pages/agency/agency_immagina/request_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/immagina_project_data_insertion_pupup.dart';
import 'package:newarc_platform/widget/UI/immagina_professionals_data_insertion_pupup.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class AgencyImmaginaView extends StatefulWidget {
  AgencyImmaginaView({
    super.key,
    this.updateViewCallback,
    required this.agencyUser,
    this.projectArguments = const {},
    this.isArchieved = false,
  });

  final Function? updateViewCallback;
  final AgencyUser agencyUser;
  final Map? projectArguments;
  final bool? isArchieved;

  @override
  State<AgencyImmaginaView> createState() => _AgencyImmaginaViewState();
}

class _AgencyImmaginaViewState extends State<AgencyImmaginaView> {
  final controller = Get.put<AgencyImmaginaViewController>(AgencyImmaginaViewController());

  @override
  void initState() {
    super.initState();
    controller.isRequest = widget.projectArguments?['isFromRequest'] ?? true;
    controller.clearFilter();
    log("widget.agencyUser.agencyId ===> ${widget.agencyUser.agencyId}");

    initialFetch(force: true);
  }


  void reloadAfterPop({bool force = false}) {
    controller.projects = [];
    controller.documentList = [];
    controller.filters = [];
    controller.cacheFireStore = [];
    controller.totalRecords = 0;
    controller.currentlyShowing = '';
    controller.recordsPerPage = 20;
    controller.pageCounter = 1;
    controller.totalPages = 0;
    controller.disablePreviousButton = true;
    controller.disableNextButton = false;
    controller.statusFilterController.clear();
    controller.cacheFireStore.clear();
    initialFetch(force: force);
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingProperties == true) return;
                    fetchPrevProperties();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingProperties == true) return;

                    fetchNextProperties();
                  },
                ),
                SizedBox(width: 14.0),
              ],
            ),
          )),
    );
  }

  _initialFetch({bool force = false}) {
    initialFetch(force: force);
  }

  Future<void> initialFetch({bool force = false,bool reloadAll = false}) async {
    if (controller.projects.isNotEmpty && !force && !reloadAll) return;

    controller.pageCounter = 1;

    setState(() {
      controller.loadingProperties = true;
      controller.projects = [];
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);
      collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }

        collectionSnapshot = await collectionSnapshotQuery
            // .where('requestStatus', whereIn: [CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato])
            .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
            .where("isArchived", isEqualTo: false)
            .where("isAgencyArchived", isEqualTo: false)
            .orderBy('insertionTimestamp', descending: true)
            .limit(controller.recordsPerPage)
            .get();

        collectionSnapshotCounter = await collectionSnapshotCounterQuery
            // .where('requestStatus', whereIn: [CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato])
            .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
            .where("isArchived", isEqualTo: false)
            .where("isAgencyArchived", isEqualTo: false)
            .orderBy('insertionTimestamp', descending: true)
            .get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }
      controller.documentList = collectionSnapshot.docs;

      await generateDataRows(collectionSnapshot);
      if (mounted) {
        setState(() {
          controller.loadingProperties = false;
        });
      }
    } catch (e,s) {
      print("initialFetch Agency Error ==> ${e}");
      print("initialFetch Agency Stack Trace ==> ${s}");
      if (mounted)
        setState(() {
          controller.loadingProperties = false;
          print(e);
        });
    }
  }

  fetchNextProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
          collectionSnapshot = await collectionSnapshotQuery
              .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
              .where("isArchived", isEqualTo: false)
              .where("isAgencyArchived", isEqualTo: false)
              .orderBy('insertionTimestamp', descending: true)
              .startAfterDocument(controller.documentList[controller.documentList.length - 1])
              .limit(controller.recordsPerPage)
              .get();
      }

      controller.documentList = collectionSnapshot.docs;
      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        print(e);
        controller.loadingProperties = false;
      });
    }
  }

  fetchPrevProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS);
        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
          collectionSnapshot = await collectionSnapshotQuery
              .where('agencyId', isEqualTo: widget.agencyUser.agencyId)
              .where("isArchived", isEqualTo: false)
              .where("isAgencyArchived", isEqualTo: false)
              .orderBy('insertionTimestamp', descending: true)
              .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
              .limit(controller.recordsPerPage)
              .get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFireStore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) {
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFireStore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<ImmaginaProject> _projects = [];


    for (var element in collectionSnapshot.docs) {
      ImmaginaProject _tmp = ImmaginaProject.fromDocument(element.data(), element.id);
      _projects.add(_tmp);
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;

    if (_projects.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_projects.length > 0 && _projects.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _projects.length).toString();
    }
    if (mounted){
      setState(() {
        controller.projects = _projects;
        controller.loadingProperties = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _headerTitle(),
                Expanded(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      _agencySubscriptionDetails(context),
                    ],
                  ),
                )
              ],
            ),
            _filter(),
            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColor.white,
                border: Border.all(width: 1.5, color: AppColor.borderColor),
              ),
              child: Column(
                children: [
                  //_tabBar(),
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                            opacity: 1,
                            child:
                            NewarcDataTable(
                                    rowsPerPage: 20,
                                    isHasDecoration: false,
                                    hidePaginator: true,
                                    onPageChanged: (val) {
                                      print("page : $val");
                                    },
                                    source: AgencyRequestSource(
                                      isRequest: controller.isRequest,
                                      projects: controller.projects,
                                      onAddressTap: (project) {
                                        bool isReq = [CommonUtils.preventivazione,CommonUtils.daCompletare,CommonUtils.inAnalisi,CommonUtils.bloccata].contains(project.requestStatus);
                                        bool isProf = project.isForProfessionals;
                                        widget.projectArguments!.clear();
                                        widget.projectArguments!.addAll({
                                          'projectFirebaseId': project.id,
                                          'property': null,
                                          'agencyUser': widget.agencyUser,
                                          'updateViewCallback': widget.updateViewCallback,
                                          'initialFetchProperties': _initialFetch(force: true),
                                          'isFromRequest': isReq,
                                          'isForProfessionals': isProf,
                                        });

                                        if(isReq){
                                          widget.updateViewCallback!('inside-request', projectArguments: widget.projectArguments);
                                        }else{
                                          widget.updateViewCallback!('inside-project', projectArguments: widget.projectArguments);
                                        }

                                      },
                                    ),
                                    columns: [
                                      DataColumn2(label: Text("Indirizzo")),
                                      DataColumn2(label: Text("Stato progetto")),
                                      DataColumn2(label: Text("Inizio progetto")),
                                      DataColumn2(label: Text("Fine progetto")),
                                    ],
                                  )),
                        if (controller.loadingProperties)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: dataTablePagination(),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _agencySubscriptionDetails(BuildContext context) {
    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream: FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(widget.agencyUser.agencyId).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox();
        }

        if (snapshot.hasError) {
          return SizedBox();
        }

        if (!snapshot.hasData || !snapshot.data!.exists) {
          return SizedBox();
        }

        final agencyData = snapshot.data!.data();
        final int subscriptionServiceCountLeft = agencyData?['subscriptionServiceCountLeft'] ?? 0;
        final int subscriptionServiceCount = agencyData?['subscriptionServiceCount'] ?? 0;
        DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(agencyData?['subscriptionEndDate'] ?? 0);
        DateTime now = DateTime.now();

        final bool isSubscriptionOver = (subscriptionServiceCountLeft <= 0 || now.isAfter(expiryDate));

        return Row(
          children: [
            Container(
              height: 32,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: const Color(0xffF1F1F1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        NarFormLabelWidget(
                          label: "Crediti",
                          fontSize: 10,
                          fontWeight: '600',
                          textColor: isSubscriptionOver ? const Color(0xffDD6666) : Theme.of(context).primaryColor,
                        ),
                        NarFormLabelWidget(
                          label: "$subscriptionServiceCountLeft/$subscriptionServiceCount",
                          fontSize: 12,
                          fontWeight: '800',
                          textColor: isSubscriptionOver ? const Color(0xffDD6666) : Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                  ),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () async {
                        if (!isSubscriptionOver) {
                          controller.formMessages.clear();
                          await showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return Center(
                                child: ImmaginaDataInsertionPopup(
                                  // isStripPayOnly: false,
                                  procedureStep: 'initial',
                                  agencyUser: widget.agencyUser,
                                  onClose: () {
                                    print("dialog closed");
                                    print(widget.agencyUser);
                                    reloadAfterPop(force: true);
                                  },
                                ),
                              );
                            },
                          );
                        } else {
                          _noSubscription();
                        }
                      },
                      child: Container(
                        height: 32,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Icon(Icons.add, color: Colors.white, size: 14,),
                              SizedBox(width: 5,),
                              NarFormLabelWidget(
                                label: 'Immagina',
                                fontSize: 12,
                                fontWeight: '600',
                                letterSpacing: 1,
                                textColor: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      )
                    )
                  )
                ],
              ),
            ),
            const SizedBox(width: 16),
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () async {
                  controller.formMessages.clear();
                  await showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (BuildContext context) {
                      return Center(
                        child: ImmaginaProfessionalsDataInsertionPopup(
                          procedureStep: 'initial',
                          agencyUser: widget.agencyUser,
                          onClose: () {
                            print("dialog closed");
                            print(widget.agencyUser);
                            reloadAfterPop(force: true);
                          },
                        ),
                      );
                    },
                  );
                },
                child: Container(
                  height: 32,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Icon(Icons.add, color: Colors.white, size: 14,),
                        SizedBox(width: 5,),
                        NarFormLabelWidget(
                          label: 'Immagina for Professionals',
                          fontSize: 12,
                          fontWeight: '600',
                          letterSpacing: 1,
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _noSubscription() {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: BaseNewarcPopup(
            noButton: true,
            onPressed: () {},
            buttonText: "Chiudi",
            title: "Nuova richiesta progetto",
            column: Container(
              width: 550,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 60,
                  ),
                  Column(
                    children: [
                      SvgPicture.asset(
                        "assets/icons/coins.svg",
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      NarFormLabelWidget(
                        label: 'Nessun credito disponibile',
                        fontSize: 20,
                        fontWeight: '700',
                        textColor: AppColor.black,
                      ),
                      SizedBox(
                        height: 14,
                      ),
                      NarFormLabelWidget(
                        label:
                            'Al momento il tuo account non ha crediti a disposizione.\nPuoi effettuare una richiesta di progetto acquistandola singolarmente,\noppure attivare prima un abbonamento.',
                        fontSize: 15,
                        fontWeight: '600',
                        overflow: TextOverflow.fade,
                        textAlign: TextAlign.center,
                        textColor: Color(0xff5F5F5F),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 60,
                  ),
                  Column(
                    children: [
                      BaseNewarcButton(
                        color: Theme.of(context).primaryColor,
                        textColor: Colors.white,
                        buttonText: "Acquista un abbonamento",
                        fontWeight: '600',
                        fontSize: 15,
                        height: 40,
                        width: 260,
                        onPressed: () async {
                          Navigator.pop(context);
                          widget.updateViewCallback!(
                            'abbonamento',
                          );
                        },
                      ),
                      SizedBox(
                        height: 11,
                      ),
                      BaseNewarcButton(
                        color: AppColor.white,
                        textColor: Theme.of(context).primaryColor,
                        buttonText: "Acquista progetto singolo",
                        fontWeight: '600',
                        fontSize: 15,
                        height: 40,
                        borderColor: Theme.of(context).primaryColor,
                        width: 260,
                        onPressed: () async {
                          Navigator.pop(context);

                          controller.formMessages.clear();
                          await showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return Center(
                                child: ImmaginaDataInsertionPopup(
                                  // isStripPayOnly: true,
                                  procedureStep: 'initial',
                                  agencyUser: widget.agencyUser,
                                  onClose: () {
                                    reloadAfterPop(force: true);
                                  },
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per indirizzo o per codice...",
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<ImmaginaProject> filtered = controller.projects.where((project) {
              final address = project.addressInfo;
              final code = project.projectId.toLowerCase() ?? "";
              final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
              final streetName = address?.streetName?.toLowerCase() ?? '';
              final fullAddress = address?.fullAddress?.toLowerCase() ?? project.streetName?.toLowerCase() ?? "";
              return code.contains(searchQuery.toLowerCase()) ||
                  city.contains(searchQuery.toLowerCase()) ||
                  streetName.contains(searchQuery.toLowerCase()) ||
                  fullAddress.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.projects = filtered;
            });
          }
        }else{
          await initialFetch(force: true);
        }
      },
      suffixIconOnTap: ()async{
        await initialFetch(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<ImmaginaProject> filtered = controller.projects.where((project) {
            final address = project.addressInfo;
            final code = project.projectId.toLowerCase() ?? "";
            final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase() ?? project.streetName?.toLowerCase() ?? "";
            return code.contains(controller.searchTextController.text.toLowerCase()) ||
                city.contains(controller.searchTextController.text.toLowerCase()) ||
                streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                fullAddress.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.projects = filtered;
          });
        }else{
          await initialFetch(force: true);
        }
      },
      selectedFilters: [
        controller.statusSelectedFilter,controller.agencySelectedFilter,controller.realizzazioneFilter,controller.venditaFilter
      ],
      textEditingControllers: [controller.statusFilterController,controller.agencyFilterController,controller.realizzazioneFilterController,controller.venditaFilterController],
      filterFields: controller.isRequest ? [
        {
          'Status': NarSelectBoxWidget(
            options: [
              CommonUtils.daCompletare.toCapitalized(),
              CommonUtils.inAnalisi.toCapitalized(),
              CommonUtils.bloccata.toCapitalized(),
            ],
            onChanged: (value) {
              controller.filters = [
                {
                  'field': 'requestStatus',
                  'value': controller.statusFilterController.text.toLowerCase(),
                  'search': 'equal',
                }
              ];
              setState(() {
                controller.statusSelectedFilter = controller.statusFilterController.text.toLowerCase();
              });
            },
            controller: controller.statusFilterController,
          ),
        },
      ] : [
        {
          'Realizzazione': NarSelectBoxWidget(
            options: [
              CommonUtils.daCompletare.toCapitalized(),
              CommonUtils.inAnalisi.toCapitalized(),
              CommonUtils.bloccata.toCapitalized(),
              CommonUtils.preventivazione.toCapitalized(),
              CommonUtils.inLavorazione.toCapitalized(),
              CommonUtils.completato.toCapitalized(),
            ],
            controller: controller.realizzazioneFilterController,
            onChanged: (value) {
              controller.filters.removeWhere((element) {
                return element['field'] == 'requestStatus';
              });

              controller.filters.add({'field': 'requestStatus', 'value': controller.realizzazioneFilterController.text.toLowerCase(), 'search': 'equal'});
              controller.realizzazioneFilter = controller.realizzazioneFilterController.text;
            },
          ),
        },
      ],
      onSubmit: () async {
        await initialFetch(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        await initialFetch(force: true);
      },
    );
  }

  NarFormLabelWidget _headerTitle() {
    return NarFormLabelWidget(
      label:  controller.isRequest ? 'Richieste' :'Progetti',
      fontSize: 19,
      fontWeight: '700',
      textColor: Colors.black,
    );
  }
}
