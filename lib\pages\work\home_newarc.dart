
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/agency/inside_request_view.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_view.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_inside_view.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_view.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_inside_view.dart';
import 'package:newarc_platform/pages/work/porte_interne/porte_interne_inside_view.dart';
import 'package:newarc_platform/pages/work/porte_interne/porte_interne_view.dart';
import 'package:newarc_platform/pages/work/suggested_contacts/suggested_contacts_view.dart';
import 'package:newarc_platform/pages/work/acquired_contacts/acquired_contacts_view.dart';
import 'package:newarc_platform/pages/work/cep_view/cep_view.dart';
import 'package:newarc_platform/pages/work/common_widget.dart';
import 'package:newarc_platform/pages/work/immagina_project_review/immagina_project_review.dart';
import 'package:newarc_platform/pages/work/immagina_request_review/request_review_screen.dart';
import 'package:newarc_platform/pages/work/newarc_active_ads.dart';
import 'package:newarc_platform/pages/work/moodboard/moodboard_view.dart';
import 'package:newarc_platform/pages/work/newarc_immagina/newarc_immagina_view.dart';
import 'package:newarc_platform/pages/work/newarc_project_view.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/work/agenzie_view.dart';
import 'package:newarc_platform/pages/work/ditte_newarc.dart';
import 'package:newarc_platform/pages/work/newarc_active_projects/newarc_active_projects_view.dart';
import 'package:newarc_platform/pages/work/pavimenti/pavimenti_inside_view.dart';
import 'package:newarc_platform/pages/work/pavimenti/pavimenti_view.dart';
import 'package:newarc_platform/pages/work/renovation_contacts/renovation_contacts_view.dart';
import 'package:newarc_platform/pages/work/renovation_quotation/renovation_quotation_view.dart';
import 'package:newarc_platform/pages/work/gestione/persone/persone_view.dart';
import 'package:newarc_platform/pages/work/gestione/persone/persone.dart';
import 'package:newarc_platform/pages/common/web_leads/web_leads_view.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_inside_view.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_view.dart';
import 'package:newarc_platform/pages/work/tinte/tinte_inside_view.dart';
import 'package:newarc_platform/pages/work/tinte/tinte_view.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/work/active_ad_add.dart';
import 'package:newarc_platform/widget/work/custom_appbar_menu.dart';
import 'package:newarc_platform/widget/work/custom_drawer.dart';
import 'package:newarc_platform/widget/work/provisional_economic_account.dart';
import 'package:newarc_platform/widget/work/renovation_quotation.dart';
import 'package:newarc_platform/widget/work/settings.dart';
import 'newarc_immagina_project_archive/newarc_immagina_project_archive_view.dart';

class HomeNewarc extends StatefulWidget {
  const HomeNewarc({Key? key, required this.newarcUser}) : super(key: key);

  static const String route = '/home-newarc';

  final NewarcUser newarcUser;

  @override
  State<HomeNewarc> createState() => _HomeNewarcState();
}

class _HomeNewarcState extends State<HomeNewarc> {
  String selectedView = 'progetti-in-corso';

  ///revert back when done

  // String selectedView = 'team';

  // String selectedView = 'c-e-p';
  String? profilePicture;
  Map? _projectArguments;

  ReceivedContactsPageFilters? receivedContactsPageFilters;
  var appBarHeight = AppBar().preferredSize.height;

  @override
  void initState() {
    if (widget.newarcUser.role == 'renderist') {
      selectedView = 'progetti-attivi';
    } else {
      //implement additional logic where and when needed through if-else statements
    }
    getProfilePicture();
    super.initState();
  }

  getProfilePicture() async {
    var url = await printUrl(
        'users/', widget.newarcUser.id, widget.newarcUser.profilePicture);

    if (url != '') {
      setState(() {
        profilePicture = url;
      });
    }
    // widget.agencyUser.profilePicture!
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          if (constraints.maxWidth > 650) {
            return Scaffold(
              backgroundColor: Colors.white,
              body: Row(
                children: [
                  CustomDrawer(
                    updateViewCallback: _updateViewCallback,
                    newarcUser: widget.newarcUser,
                    selectedView: selectedView,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(right: 15),
                          color: Colors.white,
                          child: AppBar(
                            backgroundColor: Colors.white,
                            elevation: 0,
                            leading: Container(),
                            actions: <Widget>[
                              // getNotificationTray(),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        NarFormLabelWidget(
                                            label: widget.newarcUser.firstName! +
                                                ' ' +
                                                widget.newarcUser.lastName!),
                                        NarFormLabelWidget(
                                          label: appConst.userRolesDict[
                                              widget.newarcUser.role!]!,
                                          textColor: Color(0xff696969),
                                          fontWeight: '500',
                                          fontSize: 11,
                                          letterSpacing: 0.4,
                                        )
                                      ]),
                                  SizedBox(width: 10),
                                  getAppbarMenu(profilePicture),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: 1,
                          color: Color(0xffe0e0e0),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 30.0, vertical: 10),
                            child: selectView(true),
                            // child: Text('Test'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Versione ridotta
            return Scaffold(
              backgroundColor: Color(0xffF9F9F9),
              appBar: AppBar(
                backgroundColor: Colors.black,
                actions: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      // do something
                    },
                  ),
                  // getNotificationTray(),
                  PopupMenuButton(
                    tooltip: "",
                    icon: SvgPicture.asset(
                      'assets/icons/account.svg',
                      color: Colors.grey,
                      width: 20,
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Column(children: [
                          Text(widget.newarcUser.firstName!),
                          Text(widget.newarcUser.email!)
                        ]),
                        value: 1,
                        onTap: () {},
                      ),
                      PopupMenuItem(
                        child: Text("Logout"),
                        value: 2,
                        onTap: () async {
                          // deleteUserRole();
                          await FirebaseAuth.instance.signOut();
                          Navigator.of(context).pushReplacementNamed(
                            LoginPage.route,
                          );
                        },
                      ),
                    ],
                  )
                ],
              ),
              drawer: CustomDrawer(
                updateViewCallback: _updateViewCallback,
                newarcUser: widget.newarcUser,
                selectedView: selectedView,
              ),
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                child: Column(
                  children: [
                    Expanded(child: selectView(false)),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }

  void _updateViewCallback(String newSelectedView,
      {ReceivedContactsPageFilters? filter, Map projectArguments = const {}}) {
    if (projectArguments.length > 0) {
      _projectArguments = projectArguments;
    }

    setState(() {
      selectedView = newSelectedView;
    });

    /*if (newSelectedView == 'contatti-ricevuti') {
      setState(() {
        receivedContactsPageFilters = filter;
        //fare qualcosa con il filtro, passarlo alla pagina dei contatti...che bordello.
      });
    }*/
  }

  /*Widget getNotificationTray() {
    return StreamBuilder(
      stream: FirebaseFirestore.instance
          .collection(
            '${appConfig.COLLECT_AGENCIES}/',
          )
          .doc(widget.agencyUser.agency!.id)
          .snapshots(),
      builder: (BuildContext context,
          AsyncSnapshot<DocumentSnapshot<Map<String, dynamic>>> snapshot) {
        //List<NewarcNotification> notifications = [];
        bool notificationsRead = true;

        if (snapshot.hasData) {
          Agency agency =
              Agency.fromDocument(snapshot.data!.data()!, snapshot.data!.id);
          notificationsRead = agency.notificationsRead!;
          /*snapshot.data!.docs.forEach((doc) {
            notifications
                .add(NewarcNotification.fromDocument(doc.data(), doc.id));
          });*/
        }
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              height: 40,
              width: 40,
              color: Colors.transparent,
              child: CustomNotificationTray(
                  agency: widget.agencyUser.agency!,
                  notificationsRead: notificationsRead),
            ),
            notificationsRead
                ? Container()
                : Positioned(
                    right: 10,
                    top: 10,
                    child: Container(
                      width: 9,
                      height: 9,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  )
          ],
        );
      },
    );
  }*/

  Widget getAppbarMenu(String? profilePicture) {
    return Container(
      height: 40,
      width: 40,
      margin: EdgeInsets.only(right: 25),
      color: AppColor.white,
      child: CustomAppbarMenu(
        // agency: widget.agencyUser.agency!,
        profilePicture: profilePicture,
        onSettingsTapped: () {
          selectedView = 'newarc-team-settings';
          _updateViewCallback(selectedView);
        },
      ),
    );
  }

  Widget selectView(bool responsive) {
    if (selectedView == 'agenzie') {
      return AgenzieNewarc(
          // agency: widget.agencyUser.agency!,
          // agencyUser: widget.agencyUser,
          responsive: responsive,
          getProfilePicture: getProfilePicture);
    } else if (selectedView == 'agencies') {
      return AgenciesView(
          responsive: responsive, updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'agencies-inside-view') {
      return AgenciesInsideView(
          updateViewCallback: _updateViewCallback,
          agency: _projectArguments!["agency"]);
    } else if (selectedView == 'ditte') {
      return DitteNewarc(
        responsive: responsive,
      );
    } else if (selectedView == 'contractors') {
      return ContractorsView(
          responsive: responsive, updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'contractors-inside-view') {
      return ContractorsInsideView(
          updateViewCallback: _updateViewCallback,
          contractor: _projectArguments!["contractor"]);
    } else if (selectedView == 'team') {
      // return NewarcUsers(
      //   responsive: responsive,
      // );
      return PersoneView(
        key: Key('team'),
        isArchived: false,
        updateViewCallback: _updateViewCallback,
        forceDataFetch: true,
        projectArguments: {
          'current_menu': "team",
          'firebaseId': '',
          'property': '',
          'updateViewCallback': '',
          'initialFetchProperties': ''
        },
      );
    } else if (selectedView == 'team-single') {
      return PersoneInsideView(
          userId: _projectArguments!['userId'],
          updateViewCallback: _projectArguments!['updateViewCallback'],
          initialFetchPeople: _projectArguments!['initialFetchPeople']);
    } else if (selectedView == 'contatti-ricevuti') {
      return AcquiredContactsView(responsive: responsive);
    } else if (selectedView == 'web-lead') {
      return WebLeadsView(responsive: responsive, tag: 'agency');
    } else if (selectedView == 'newarc-lead') {
      return WebLeadsView(responsive: responsive, tag: 'newarc');
    } else if (selectedView == 'contatti-ristrutturazione') {
      return RenovationContactsView(responsive: responsive, newarcUser: widget.newarcUser);
    } else if (selectedView == 'segnalazioni-ristrutturazione') {
      return SuggestedContactsWorkView(responsive: responsive);
    } else if (selectedView == 'progetti-in-corso') {
      return NewarcActiveProjectsView(
          key: Key('progetti-in-corso'),
          isArchived: false,
          forceDataFetch: true,
          newarcUser: widget.newarcUser,
          // agencyUser: widget.agencyUser,
          projectArguments: {
            'current_menu': "progetti-in-corso",
            'firebaseId': '',
            'property': '',
            // 'agencyUser': '',
            'updateViewCallback': '',
            'initialFetchProperties': '',
            'isRestoration': false,
          },
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'progetti-in-corso-single') {
      return NewarcProjectView(
          newarcUser: widget.newarcUser,
          projectFirebaseId: _projectArguments!['projectFirebaseId'],
          updateViewCallback: _projectArguments!['updateViewCallback'],
          initialFetchProperties: _projectArguments!['initialFetchProperties']);
    } else if (selectedView == 'storico-progetti') {
      return NewarcActiveProjectsView(
          key: Key('storico-progetti'),
          isArchived: true,
          newarcUser: widget.newarcUser,
          projectArguments: {
            'current_menu': "storico-progetti",
            'firebaseId': '',
            'property': '',
            // 'agencyUser': '',
            'updateViewCallback': '',
            'initialFetchProperties': '',
            'isRestoration': false,
          },
          updateViewCallback: _updateViewCallback
          // agencyUser: widget.agencyUser,
          );
    } else if (selectedView == 'renovation-quotation') {
      return RenovationQuotationView(
          key: Key('renovation-quotation'),
          responsive: responsive,
          newarcUser: widget.newarcUser,
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'renovation-quotation-single') {
      return RenovationQuotationSingle(
          key: Key('renovation-quotation-single'),
          renovationQuotation: _projectArguments!['renovationQuotation'],
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'newarc-team-settings') {
      return NewarcTeamSetting(
          newarcUser: widget.newarcUser, getProfilePicture: getProfilePicture);
    } else if (selectedView == 'newarc-team-settings') {
      return NewarcTeamSetting(
          newarcUser: widget.newarcUser, getProfilePicture: getProfilePicture);
    } else if (selectedView == 'c-e-p') {
      return NewarcCEPView(
          key: Key('c-e-p'),
          isArchived: false,
          newarcUser: widget.newarcUser,
          projectArguments: {'firebaseId': '', 'updateViewCallback': ''},
          updateViewCallback: _updateViewCallback
          // agencyUser: widget.agencyUser,
          );
    } else if (selectedView == 'cep-single') {
      return ProvisionalEconomicAccount(
        key: Key('cep-single'),
        firebaseId: _projectArguments!['firebaseId'],
        updateViewCallback: _updateViewCallback,
      );
    } else if (selectedView == 'moodboard') {
      return MoodboardView(
          key: Key('moodboard'),
          responsive: responsive,
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'widget') {
      return CommonWidget(
        key: Key('widget'),
      );
    }
    else if (selectedView == 'progetti-attivi') {
      ///immagina project list
      return NewarcImmagina(
        newarcUser: widget.newarcUser,
        projectArguments: {
          'projectFirebaseId': '',
          'updateViewCallback': '',
          'initialFetchProperties': '',
          'isFromRequest': false,
        },
        updateViewCallback: _updateViewCallback,
        key: Key('progetti-attivi'),
      );
    }else if (selectedView == 'richieste') {
      ///immagina request
      return NewarcImmagina(
        newarcUser: widget.newarcUser,
        projectArguments: {
          'projectFirebaseId': '',
          'updateViewCallback': '',
          'initialFetchProperties': '',
          'isFromRequest': true,
        },
        updateViewCallback: _updateViewCallback,
        key: Key('richieste'),
      );
    }
    else if (selectedView == 'progetti-archiviati') {
      ///immagina  project archived view
      return NewarcImmaginaProjectArchiveView(
        newarcUser: widget.newarcUser,
        projectArguments: {
          'projectFirebaseId': '',
          'updateViewCallback': '',
          'initialFetchProperties': '',
        },
        updateViewCallback: _updateViewCallback,
        key: Key('progetti-archiviati'),
      );
    } else if (selectedView == 'active-ads') {
      return NewarcActiveAds(
        isArchived: false,
        updateViewCallback: _updateViewCallback,
        forceDataFetch: true,
        projectArguments: {
          'current_menu': "active-ads",
          'firebaseId': '',
          'property': '',
          'updateViewCallback': '',
          'initialFetchProperties': '',
          'forceDataFetch': true
        },
        key: Key('active-ads'),
      );
    } else if (selectedView == 'archived-ads') {
      return NewarcActiveAds(
        isArchived: true,
        updateViewCallback: _updateViewCallback,
        projectArguments: {
          'current_menu': "archived-ads",
          'firebaseId': '',
          'property': '',
          'updateViewCallback': '',
          'initialFetchProperties': '',
          'forceDataFetch': true
        },
        key: Key('archived-ads'),
      );
    } else if (selectedView == 'active-ad-single') {
      return ActiveAdAdd(
        project: _projectArguments!['project'],
        property: _projectArguments!['property'],
        isInputChangeDetected: [false],
        key: Key('active-ad-single'),
        updateViewCallback: _updateViewCallback,
      );
    } else if (selectedView == 'single-archived-ad') {
      return ActiveAdAdd(
        project: _projectArguments!['project'],
        property: _projectArguments!['property'],
        isInputChangeDetected: [false],
        key: Key('single-archived-ad'),
        updateViewCallback: _updateViewCallback,
      );
    } else if (selectedView == 'immagina-request-review') {
      return RequestReviewView(
        projectFirebaseId: _projectArguments!['projectFirebaseId'],
        updateViewCallback: _projectArguments!['updateViewCallback'],
        initialFetchProperties: _projectArguments!['initialFetchProperties'],
        isFromRequest: _projectArguments!['isFromRequest'],
        isForProfessionals: _projectArguments!['isForProfessionals'],
        projectArguments: _projectArguments,
        key: Key('immagina-request-review'),
      );
    } else if (selectedView == 'immagina-project-review') {
      return ImmaginaProjectReview(
        newarcUser: widget.newarcUser,
        projectFirebaseId: _projectArguments?['projectFirebaseId'],
        updateViewCallback: _projectArguments?['updateViewCallback'],
        initialFetchProperties: _projectArguments?['initialFetchProperties'],
        isFromRequest: _projectArguments!['isFromRequest'],
        isFromProjectArchive: _projectArguments!['isFromProjectArchive'],
        projectArguments: _projectArguments,
        key: Key('immagina-project-review'),
      );
    } else if (selectedView == 'renovation-project') {
      return NewarcActiveProjectsView(
          key: Key('renovation-project'),
          isArchived: false,
          forceDataFetch: true,
          newarcUser: widget.newarcUser,
          // agencyUser: widget.agencyUser,
          projectArguments: {
            'current_menu': "renovation-project",
            'firebaseId': '',
            'property': '',
            // 'agencyUser': '',
            'updateViewCallback': '',
            'initialFetchProperties': '',
            'isRestoration': true,
          },
          updateViewCallback: _updateViewCallback);
    }
    // Remove this else if (selectedView == 'inside-request') if found
    else if (selectedView == 'inside-request') {
      return InsideRequestView(
        key: Key('inside-request'),
        isFromRequest: _projectArguments!['isFromRequest'],
        initialFetchProperties: _projectArguments!['initialFetchProperties'],
        agencyUser: _projectArguments!['agencyUser'],
        projectArguments: _projectArguments,
        updateViewCallback: _projectArguments!['updateViewCallback'],
        projectFirebaseId: _projectArguments!['projectFirebaseId'],
      );
    } else if (selectedView == 'pavimenti') {
      return PavimentiView(
          key: Key('pavimenti'),
          responsive: responsive,
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'pavimenti-inside-view') {
      return PavimentiInsideView(
        key: Key('pavimenti-inside-view'),
        updateViewCallback: _updateViewCallback,
        naMaterialModel: _projectArguments?["naMaterialModel"],
      );
    } else if (selectedView == 'rivestimenti') {
      return RivestimentiView(
          key: Key('rivestimenti'),
          responsive: responsive,
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'rivestimenti-inside-view') {
      return RivestimentiInsideView(
        key: Key('rivestimenti-inside-view'),
        updateViewCallback: _updateViewCallback,
        naMaterialModel: _projectArguments?["naMaterialModel"],
      );
    } else if (selectedView == 'tinte') {
      return TinteView(
          key: Key('tinte'),
          responsive: responsive,
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'tinte-inside-view') {
      return TinteInsideView(
        key: Key('tinte-inside-view'),
        updateViewCallback: _updateViewCallback,
        naMaterialModel: _projectArguments?["naMaterialModel"],
      );
    } else if (selectedView == 'porte-interne') {
      return PorteInterneView(
          key: Key('porte-interne'),
          responsive: responsive,
          updateViewCallback: _updateViewCallback);
    } else if (selectedView == 'porte-interne-inside-view') {
      return PorteInterneInsideView(
        key: Key('porte-interne-inside-view'),
        updateViewCallback: _updateViewCallback,
        naMaterialModel: _projectArguments?["naMaterialModel"],
      );
    }
    // Remove this else if (selectedView == 'inside-request') if found
    else {
      return Text('La vista selezionata non è disponibile');
    }
  }
}
