import 'dart:async';
import 'dart:developer';
import 'dart:html' as html;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/tab/common_dropdown_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../../functions/various.dart';
import '../../../utils/storage.dart';
import '../../../widget/UI/base_newarc_button.dart';
import '../../../widget/UI/file-picker.dart';
import 'dart:js' as js;

class RequestReviewView extends StatefulWidget {
  const RequestReviewView({
    super.key,
    this.updateViewCallback,
    this.projectArguments = const {},
    this.projectFirebaseId,
    this.initialFetchProperties,
    this.isFromRequest = true,
    this.isForProfessionals = false,
  });

  final Function? updateViewCallback;
  final Map? projectArguments;

  final String? projectFirebaseId;

  final Function? initialFetchProperties;
  final bool? isFromRequest;
  final bool? isForProfessionals;

  @override
  State<RequestReviewView> createState() => _RequestReviewViewState();
}

class _RequestReviewViewState extends State<RequestReviewView> {
  bool loading = true;
  ImmaginaProject immaginaProject = ImmaginaProject.empty();
  String selectedStatus = CommonUtils.inAnalisi.toCapitalized();
  TextEditingController selectedReason =
      TextEditingController(text: "Fotografie non conformi");
  TextEditingController notesController = TextEditingController();

  List<String> status = [];
  List<String> selectedReasonList = [
    "Fotografie non conformi",
    "Planimetrie non conformi"
  ];

  List<NewarcUser> projectRenderistList = [];
  List<NewarcUser> allRenderistList = [];
  // TextEditingController renderistNotesController = TextEditingController();

  bool isImagesDownload = false;

  @override
  void initState() {
    initialFetch();
    super.initState();
  }

  initialFetch() async {
    try {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
          .doc(
            widget.projectFirebaseId,
          )
          .get();

      if (collectionSnapshot.data() != null) {
        immaginaProject.copy(
          ImmaginaProject.fromDocument(
              collectionSnapshot.data()!, widget.projectFirebaseId!),
        );

        if (immaginaProject.wantsNewarcPictures == true ||
            immaginaProject.wantsNewarcPlanimetry == true) {
          status = [
            CommonUtils.inAnalisi.toCapitalized(),
            CommonUtils.confermata.toCapitalized()
          ];
        } else {
          status = [
            CommonUtils.inAnalisi.toCapitalized(),
            CommonUtils.bloccata.toCapitalized(),
            CommonUtils.confermata.toCapitalized()
          ];
        }
        selectedStatus = immaginaProject.requestStatus.toCapitalized();
        if (selectedStatus == CommonUtils.preventivazione.toCapitalized()){
          status = [
            CommonUtils.preventivazione.toCapitalized(),
            CommonUtils.confermata.toCapitalized()
          ];
        }
        /// populate all renderists list
        final renderistUsers = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', whereIn: ['master-renderist', 'renderist'])
          .where('isArchived', isEqualTo: false)
          .get();
        for (var userDoc in renderistUsers.docs) {
          final userData = userDoc.data();
          allRenderistList.add(NewarcUser.fromDocument(userData, userDoc.id));
        }
        /// populate projects' renderists team list
        projectRenderistList = allRenderistList.where((user) => immaginaProject.renderistTeamIdList.contains(user.id)).toList();
      }

    } catch (e) {
      print(e);
    }finally{
      setState(() {
        loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: loading
                ?
            SizedBox(
              height: MediaQuery.sizeOf(context).height,
              child: Center(
                  child: CircularProgressIndicator(
                color: Theme.of(context).primaryColor,
              ),
              ),
            )
                : Column(
              children: [
                _header(),
                SizedBox(
                  height: 10,
                ),
                Container(
                  decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.circular(13),
                    border: Border.all(
                      width: 1,
                      color: Color(0xffE7E7E7),
                    ),
                  ),
                  padding: EdgeInsets.all(30),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      !(immaginaProject.isForProfessionals)
                      ? Expanded(
                        flex: 4,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _location(immaginaProject),
                            SizedBox(
                              height: 26,
                            ),
                            _motionless(immaginaProject),
                            SizedBox(
                              height: 26,
                            ),
                            _description(immaginaProject),
                            SizedBox(
                              height: 26,
                            ),
                            _characteristics(immaginaProject),
                            Visibility(
                              visible: immaginaProject.wantsNewarcPlanimetry ==
                                  false,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _floorPlan(immaginaProject),
                                ],
                              ),
                            ),
                            Visibility(
                              visible:
                                  immaginaProject.wantsNewarcPictures == false,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _photographs(immaginaProject.pictures),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 26,
                            ),
                            _problems(immaginaProject),
                            SizedBox(
                              height: 26,
                            ),
                            _urgencyAns(immaginaProject),
                            SizedBox(
                              height: 26,
                            ),
                          ],
                        ),
                      )
                      : Expanded(
                        flex: 4,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: immaginaProject.childrenProjects!.map((prog){
                            int childIndex = immaginaProject.childrenProjects!.indexOf(prog);
                            return Container(
                              // width: 100,
                              // height: 100,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(13),
                                border: Border.all(
                                  width: 1,
                                  color: Color(0xffE7E7E7),
                                ),
                              ),
                              padding: EdgeInsets.all(30),
                              margin: EdgeInsets.only(bottom: 26),
                              child: Column(
                                children: [
                                  NarFormLabelWidget(
                                    label: "Taglio ${childIndex +1} - ${prog.grossSquareFootage ?? ""} mq",
                                    fontSize: 16,
                                    fontWeight: '700',
                                    textColor: Colors.black,
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  _location(prog),
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _motionless(prog),
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _description(immaginaProject),
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _characteristics(prog),
                                  // TODO show taglio's planimetry
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _professionalsFloorPlan(immaginaProject, prog),
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _problems(immaginaProject),
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _urgencyAns(immaginaProject),
                                  SizedBox(
                                    height: 26,
                                  ),
                                  _optionals(immaginaProject),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(
                          children: [
                            CustomDropdownButton(
                              width: 180,
                              selectedValue: selectedStatus,
                              items: status,
                              showCircle: true,
                              hintText: 'Select Status',
                              containerColor: Colors.green,
                              getColor: (status) {
                                switch (status) {
                                  case 'Da sbloccare':
                                    return Color.fromRGBO(166, 166, 166, 1);
                                  case 'Preventivazione':
                                    return Color(0xff5DB1E3);
                                  case 'Da contattare':
                                    return Color.fromRGBO(245, 198, 32, 1);
                                  case 'Contattato':
                                    return Color.fromRGBO(86, 195, 229, 1);
                                  case 'Completato':
                                    return Color.fromRGBO(72, 155, 121, 1);
                                  case 'In analisi':
                                    return Color(0xffFEC600);
                                  case 'Bloccata':
                                    return Color(0xffDD0000);
                                  case 'Confermata':
                                    return Color(0xff39C14F);
                                  case 'completato':
                                    return Color(0xff39C14F);
                                  case 'In lavorazione':
                                    return Color(0xffFF7B00);
                                  default:
                                    return Colors.transparent;
                                }
                              },
                              onChanged: (value) async {
                                if (value != null) {
                                  var docRefForAgency = await FirebaseFirestore.instance
                                      .collection(appConfig.COLLECT_AGENCIES)
                                      .doc(immaginaProject.agencyId).get();
                                  var docRef = FirebaseFirestore.instance
                                        .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                                        .doc(widget.projectFirebaseId);
                                        
                                  Agency agency = Agency.fromDocument(docRefForAgency.data()!, immaginaProject.agencyId);
                                  selectedStatus = value;

                                  /// BLOCK
                                  if (value.toLowerCase() ==
                                      CommonUtils.bloccata) {
                                    _changeStatus(agency: agency);
                                  /// CONFIRM
                                  } else if (value.toLowerCase() ==
                                      CommonUtils.confermata) {
                                    _showRenderistTeamDialog(agency: agency);
                                  /// CHANGE STATUS
                                  } else {
                                    print(selectedStatus.toLowerCase());
                                    
                                    await docRef.update({
                                      'requestStatus': value.toLowerCase(),
                                      'statusChangedDate':
                                          DateTime.now().millisecondsSinceEpoch,
                                    }).then((value) async {
                                      Fluttertoast.showToast(
                                        msg: "Lo stato è stato cambiato!",
                                        toastLength: Toast.LENGTH_SHORT,
                                        gravity: ToastGravity.BOTTOM,
                                        timeInSecForIosWeb: 1,
                                        backgroundColor: Colors.green,
                                        textColor: Colors.white,
                                        fontSize: 16.0,
                                      );
                                      await initialFetch();
                                      widget.updateViewCallback!(
                                          'progetti-attivi');
                                    }).catchError((error) {
                                      Fluttertoast.showToast(
                                        msg:
                                            "Errore durante la modifica dello stato!",
                                        toastLength: Toast.LENGTH_SHORT,
                                        gravity: ToastGravity.BOTTOM,
                                        timeInSecForIosWeb: 1,
                                        backgroundColor: Colors.red,
                                        textColor: Colors.white,
                                        fontSize: 16.0,
                                      );
                                      print("Error updating document: $error");
                                    });
                                  }
                                }
                              },
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            if (immaginaProject.requestStatus.toLowerCase() == CommonUtils.bloccata)
                              Container(
                                width: 180,
                                padding: EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7),
                                    border: Border.all(
                                      width: 1,
                                      color: Color(0xffEDAAAA),
                                    ),
                                    color: Color(0xffFDEEEE)),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Motivo del blocco",
                                      fontSize: 11,
                                      fontWeight: '600',
                                      textColor: Color(0xffE82525),
                                    ),
                                    SizedBox(
                                      height: 4,
                                    ),
                                    NarFormLabelWidget(
                                      label: immaginaProject.blockedSection,
                                      fontSize: 13,
                                      overflow: TextOverflow.fade,
                                      textAlign: TextAlign.center,
                                      fontWeight: '700',
                                      textColor: AppColor.black,
                                    ),
                                    SizedBox(
                                      height: 8,
                                    ),
                                    NarFormLabelWidget(
                                      label: immaginaProject.blockedNotes,
                                      fontSize: 12,
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.fade,
                                      fontWeight: '600',
                                      textColor: AppColor.black,
                                    ),
                                  ],
                                ),
                              )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Container _problems(ImmaginaProject proj) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Problematiche o difficoltà strutturali",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: NarFormLabelWidget(
                        fontSize: 14,
                        overflow: TextOverflow.fade,
                        fontWeight: '600',
                        textColor: AppColor.black,
                        label: proj.specialHints ?? "-",
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Container _urgencyAns(ImmaginaProject proj) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Quando vorresti mettere in vendita l’immobile?",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 10,
          ),
          NarFormLabelWidget(
            fontSize: 13,
            overflow: TextOverflow.fade,
            fontWeight: '600',
            textColor: AppColor.greyColor,
            label: "Seleziona una risposta",
          ),
          SizedBox(
            height: 5,
          ),
          Container(
            width: 350,
            height: 51,
            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(7),
            ),
            child: NarFormLabelWidget(
              fontSize: 14,
              overflow: TextOverflow.fade,
              fontWeight: '600',
              textColor: AppColor.black,
              label: proj.propertyUpForSaleAnswer ?? "-",
            ),
          )
        ],
      ),
    );
  }

  Container _optionals(ImmaginaProject proj) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: "Optionals",
                fontSize: 15,
                fontWeight: '700',
                textColor: Colors.black,
              ),
              SizedBox(
                height: 10,
              ),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: proj.optionals.map((opt) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: NarFormLabelWidget(
                        label: opt,
                        fontSize: 14,
                        fontWeight: '600',
                        textColor: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
              )
            ],
          ),
        ],
      ),
    );
  }

  Container _floorPlan(ImmaginaProject proj) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Planimetria",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 10,
          ),
          if (proj.planimetry.isNotEmpty)
            Row(
              children: [
                Expanded(
                  child: FutureBuilder<List<dynamic>>(
                    future: _fetchPlanmetriaFromFolder(proj.planimetry),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      } else if (snapshot.hasError) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: NarFormLabelWidget(
                            label: "Error fetching images: ${snapshot.error}",
                            fontSize: 15,
                            fontWeight: '700',
                            textColor: Colors.black,
                          ),
                        );
                      } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: NarFormLabelWidget(
                            label: "No images found.",
                            fontSize: 15,
                            fontWeight: '700',
                            textColor: Colors.black,
                          ),
                        );
                      } else {
                        final urls = snapshot.data!;
                        final List fileNames = urls
                            .map<String>((item) => item['filePath'].split('/').last as String)
                            .toList();

                        return NarFilePickerWidget(
                          allowMultiple: false,
                          filesToDisplayInList: 0,
                          removeButton: false,
                          isDownloadable: false,
                          removeButtonText: 'Elimina',
                          removeButtonTextColor: Color(0xff797979),
                          uploadButtonPosition: 'back',
                          showMoreButtonText: '+ espandi',
                          actionButtonPosition: 'bottom',
                          displayFormat: 'inline-widget',
                          containerWidth: 150,
                          containerHeight: 150,
                          containerBorderRadius: 8,
                          borderRadius: 7,
                          fontSize: 11,
                          fontWeight: '600',
                          showTitle: false,
                          text: '',
                          borderSideColor: Theme.of(context).primaryColor,
                          hoverColor: Color.fromRGBO(133, 133, 133, 1),
                          allFiles: fileNames,
                          pageContext: context,
                          storageDirectory: "immaginaProjects/${widget.projectFirebaseId}/planimetrie",
                          removeExistingOnChange: false,
                          progressMessage: [''],
                          notAccent: true,
                          splashColor: Color(0xffE5E5E5),
                          height: 35,
                          buttonWidth: 125,
                          buttonTextColor: Colors.black,
                          onUploadCompleted: () {
                            if (mounted) {
                              setState(() {});
                            }
                          },
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Container _professionalsFloorPlan(ImmaginaProject proj, ImmaginaProject child) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Planimetria",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 10,
          ),
          if (child.planimetry.isNotEmpty)
            Row(
              children: [
                Expanded(
                  child: FutureBuilder<List<dynamic>>(
                    future: _fetchPlanmetriaFromFolder(child.planimetry),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      } else if (snapshot.hasError) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: NarFormLabelWidget(
                            label: "Error fetching images: ${snapshot.error}",
                            fontSize: 15,
                            fontWeight: '700',
                            textColor: Colors.black,
                          ),
                        );
                      } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: NarFormLabelWidget(
                            label: "No images found.",
                            fontSize: 15,
                            fontWeight: '700',
                            textColor: Colors.black,
                          ),
                        );
                      } else {
                        final urls = snapshot.data!;
                        final List fileNames = urls
                            .map<String>((item) => item['filePath'].split('/').last as String)
                            .toList();

                        return NarFilePickerWidget(
                          allowMultiple: false,
                          filesToDisplayInList: 0,
                          removeButton: false,
                          isDownloadable: false,
                          removeButtonText: 'Elimina',
                          removeButtonTextColor: Color(0xff797979),
                          uploadButtonPosition: 'back',
                          showMoreButtonText: '+ espandi',
                          actionButtonPosition: 'bottom',
                          displayFormat: 'inline-widget',
                          containerWidth: 150,
                          containerHeight: 150,
                          containerBorderRadius: 8,
                          borderRadius: 7,
                          fontSize: 11,
                          fontWeight: '600',
                          showTitle: false,
                          text: '',
                          borderSideColor: Theme.of(context).primaryColor,
                          hoverColor: Color.fromRGBO(133, 133, 133, 1),
                          allFiles: fileNames,
                          pageContext: context,
                          storageDirectory: "immaginaProjects/${widget.projectFirebaseId}/tagli/${child.id}",
                          removeExistingOnChange: false,
                          progressMessage: [''],
                          notAccent: true,
                          splashColor: Color(0xffE5E5E5),
                          height: 35,
                          buttonWidth: 125,
                          buttonTextColor: Colors.black,
                          onUploadCompleted: () {
                            if (mounted) {
                              setState(() {});
                            }
                          },
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Container _photographs(List<dynamic> filePaths) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: "Fotografie",
                fontSize: 15,
                fontWeight: '700',
                textColor: Colors.black,
              ),
              BaseNewarcButton(
                buttonText: isImagesDownload
                    ? "Downloading"
                    : "Scarica tutte",
                fontSize: 13,
                color: AppColor.buttonBorderColor,
                textColor: AppColor.black,
                horizontalPadding: 10,
                fontWeight: "600",
                width: 120,
                height: 30,
                onPressed: () async {
                  if (immaginaProject.pictures.isNotEmpty) {
                    List<String> images = [];
                    try {
                      setState(() {
                        isImagesDownload = true;
                      });

                      List<Future<String>> urlFutures = immaginaProject.pictures.map((e) async {
                        String fineName = e['file'].split('/').last;
                        return await printUrl(
                            "immaginaProjects/${widget.projectFirebaseId}/fotografie",
                            '',
                            fineName);
                      }).toList();


                      images = await Future.wait(urlFutures);
                      if (images.isNotEmpty) {
                        await js.context.callMethod(
                            'zipAndDownloadFiles', [
                          js.JsArray.from(images),
                          "fotografie",
                          "image"
                        ]);
                      }
                    } finally {
                      setState(() {
                        isImagesDownload = false;
                      });
                    }
                  }
                },

              )
            ],
          ),
          SizedBox(
            height: 10,
          ),
          if (immaginaProject.pictures.isNotEmpty)
            SizedBox(
              width: double.infinity,
              child: FutureBuilder<List<dynamic>>(
                future: _fetchImagesFromFolder(immaginaProject.pictures),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  } else if (snapshot.hasError) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: NarFormLabelWidget(
                        label: "Error fetching images: ${snapshot.error}",
                        fontSize: 15,
                        fontWeight: '700',
                        textColor: Colors.black,
                      ),
                    );
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: NarFormLabelWidget(
                        label: "No images found.",
                        fontSize: 15,
                        fontWeight: '700',
                        textColor: Colors.black,
                      ),
                    );
                  } else {
                    final urls = snapshot.data!;
                    final List fileNames = urls
                        .map<String>((item) => item['filePath'].split('/').last as String)
                        .toList();
                    return NarFilePickerWidget(
                      allowMultiple: false,
                      filesToDisplayInList: 0,
                      removeButton: false,
                      isDownloadable: false,
                      removeButtonText: 'Elimina',
                      removeButtonTextColor: Color(0xff797979),
                      uploadButtonPosition: 'back',
                      showMoreButtonText: '+ espandi',
                      actionButtonPosition: 'bottom',
                      displayFormat: 'inline-widget',
                      containerWidth: 150,
                      containerHeight: 150,
                      containerBorderRadius: 8,
                      borderRadius: 7,
                      fontSize: 11,
                      fontWeight: '600',
                      showTitle: false,
                      text: '',
                      borderSideColor: Theme.of(context).primaryColor,
                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                      allFiles: fileNames,
                      pageContext: context,
                      storageDirectory: "immaginaProjects/${widget.projectFirebaseId}/fotografie",
                      removeExistingOnChange: false,
                      progressMessage: [''],
                      notAccent: true,
                      splashColor: Color(0xffE5E5E5),
                      height: 35,
                      buttonWidth: 125,
                      buttonTextColor: Colors.black,
                      onUploadCompleted: () {
                        if (mounted) {
                          setState(() {});
                        }
                      },
                    );
                  }
                },
              ),
            )
        ],
      ),
    );
  }

  viewImage(BuildContext context, String fileurl) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) {
        return FutureBuilder<Size>(
          future: _calculateImageDimension(context, fileurl),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      CircularProgressIndicator(color: Theme.of(context).primaryColor,),
                      SizedBox(height:5),
                      NarFormLabelWidget(label: 'Loading...', textColor: Colors.white,)
                    ],
                  )
              );
            } else if (snapshot.hasError) {
              return AlertDialog(
                title: NarFormLabelWidget(label: 'Error'),
                content: NarFormLabelWidget(label: 'Could not load image'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: NarFormLabelWidget(label: 'Close'),
                  ),
                ],
              );
            } else {
              final size = snapshot.data!;

              double imageWidth = 0;
              double imageHeight = 0;
              double displayWidth = 0;
              double displayHeight = 0;

              double maxImageWidth = MediaQuery.of(context).size.width;
              double maxImageHeight = MediaQuery.of(context).size.height;

              imageWidth = size.width.toDouble();
              imageHeight = size.height.toDouble();
              double aspectRatio = imageWidth / imageHeight;

              displayWidth = imageWidth;
              displayHeight = imageHeight;

              if (displayWidth > maxImageWidth) {
                displayWidth = maxImageWidth;
                displayHeight = displayWidth / aspectRatio;
              }

              if (displayHeight > maxImageHeight) {
                displayHeight = maxImageHeight;
                displayWidth = displayHeight * aspectRatio;
              }

              return Center(
                child: Wrap(
                  children: [
                    Material(
                      color: Colors.transparent,

                      child:
                      Center(
                        child: Stack(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                    width: displayWidth,
                                    height: displayHeight,
                                    padding: const EdgeInsets.all(0),
                                    child: ListView(
                                      padding: EdgeInsets.all(0),
                                      shrinkWrap: true,
                                      children: [
                                        Card(
                                          color: const Color.fromRGBO(255, 255, 255, 1),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15.0),
                                          ),
                                          clipBehavior: Clip.hardEdge,
                                          child: Column(
                                            children: [
                                              Container(
                                                color: const Color.fromARGB(255, 228, 228, 228),
                                                width: displayWidth,
                                                height: displayHeight,
                                                child: Image.network(
                                                  fileurl,
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    )),
                                // loadingMedia
                                //     ? NarFormLabelWidget(label: "loading media")
                                //     : SizedBox(height: 0)
                              ],
                            ),
                            Positioned(
                              top: 10,
                              right: 70,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                      onPressed: () {
                                        downloadFile(fileurl);
                                      },
                                      splashRadius: 20,
                                      icon: Icon(
                                        Icons.download,
                                        color: Colors.black,
                                      )
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 10,
                              right: 10,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      splashRadius: 20,
                                      icon: Icon(
                                        Icons.close,
                                        color: Colors.black,
                                      )),
                                ),
                              ),
                            ),
                            // Positioned(
                            //   left: 0,
                            //   top: displayHeight / 2,
                            //   child: widget.allFiles!.length > 1
                            //       ? previousButton(context, filename)
                            //       : SizedBox(height: 0),
                            // ),
                            // Positioned(
                            //   right: 0,
                            //   top: displayHeight / 2,
                            //   child: widget.allFiles!.length > 1
                            //       ? nextButton(context, filename)
                            //       : SizedBox(height: 0),
                            // )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        );
      },
    );
  }

  Future<Size> _calculateImageDimension(BuildContext context, String url) {
    Completer<Size> completer = Completer();
    Image image = Image.network(url);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(Size(
          info.image.width.toDouble(),
          info.image.height.toDouble(),
        ));
      }),
    );
    return completer.future;
  }

  downloadFile(String url) async {
    List<String> split0 = url.split('/');
    List<String> split1 = split0[split0.length - 1].split('?');
    List<String> split2 = split1[0].split('%2F');
    String filename = split2[split2.length - 1];

    // print({'download', filename, url});

    html.AnchorElement anchorElement = new html.AnchorElement(href: url);

    anchorElement.download = filename;
    anchorElement.target = '_blank';
    anchorElement.click();
  }

  Future<List<dynamic>> _fetchPlanmetriaFromFolder(List<dynamic> images) async {
    try {
      List<dynamic> data = [];

      for (var item in images) {
        data.add({
          "filePath": item,
        });
      }

      return data;
    } catch (e) {
      print('Error fetching files from folder: $e');
      return [];
    }
  }

  Future<List<dynamic>> _fetchImagesFromFolder(List<dynamic> images) async {
    try {
      List<dynamic> data = [];

      for (var item in images) {
        String filePath = item['file'];
        String tag = item['tag'];
        data.add({
          "filePath": filePath,
          "tag": tag,
        });
      }

      return data;
    } catch (e) {
      print('Error fetching files from folder: $e');
      return [];
    }
  }

  Container _location(ImmaginaProject proj) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Localizzazione",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Indirizzo",
                      controller: TextEditingController(
                        text: "${immaginaProject.streetName ?? ''}",
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Numero civico",
                      controller: TextEditingController(
                        text: immaginaProject.streetNumber,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 47,
              ),
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Città",
                      controller: TextEditingController(
                        text: "${immaginaProject.city ?? ''}",
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 16,
          ),
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Zona",
                      controller: TextEditingController(
                        text: immaginaProject.marketZone ?? "None",
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _changeStatus({required Agency agency}) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Specifica motivo del blocco",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 30,
              ),
              NarSelectBoxWidget(
                options: selectedReasonList,
                onChanged: (value) {},
                controller: selectedReason,
              ),
              SizedBox(
                height: 15,
              ),
              SizedBox(
                width: 400,
                child: Row(
                  children: [
                    CustomTextFormField(
                      flex: 1,
                      label: '',
                      hintText: "Aggiungi note",
                      minLines: 4,
                      controller: notesController,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 30,
              ),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () async {
                    String reason = selectedReason.text;
                    String notes = notesController.text;



                    var docRef = FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                        .doc(widget.projectFirebaseId);
                    await docRef.update({
                      'requestStatus': CommonUtils.bloccata,
                      'blockNotes': notes,
                      'blockedSection': reason,
                    }).then((value) {
                      // request block send notification mail
                      sendEmail(templateId: CommonUtils.requestBlockedEmailTemplateId, agency: agency, subject: CommonUtils.requestBlockedEmailSubject);
                      Fluttertoast.showToast(
                        msg: "Blocco confermato con successo!",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.green,
                        textColor: Colors.white,
                        fontSize: 16.0,
                      );
                      Navigator.pop(context);

                      initialFetch();
                    }).catchError((error) {
                      Fluttertoast.showToast(
                        msg: "Errore durante il blocco!",
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 1,
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                        fontSize: 16.0,
                      );
                      print("Error updating document: $error");
                    });
                  },
                  child: Container(
                    height: 40,
                    width: 210,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Color(0xffE82525),
                      borderRadius: BorderRadius.circular(7),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: NarFormLabelWidget(
                        label: "Conferma blocco",
                        textAlign: TextAlign.center,
                        textColor: AppColor.white,
                        fontWeight: '600',
                        fontSize: 15,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 30,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  Future<void> _showRenderistTeamDialog({required Agency agency}) {
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (__context, _setState) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Assegna il team",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 30,
              ),
              NarFormLabelWidget(
                label: "Seleziona Team",
                fontSize: 15,
                fontWeight: '500',
                textColor: AppColor.iconGreyColor,
              ),
              SizedBox(
                height: 5,
              ),
              MultiSelectDropdownWidget(
                dialogTitle: "Assegna Team Renderist",
                options: allRenderistList.map((user) => {'value': user.id, 'label': "${user.firstName ?? ""} ${user.lastName ?? ""}"}).toList(),
                initialValue: projectRenderistList.map((user) => {'value': user.id, 'label': "${user.firstName ?? ""} ${user.lastName ?? ""}"}).toList(),
                onChanged: (List<dynamic> selectedValues) {
                  print(selectedValues);
                  projectRenderistList = selectedValues.map((elem) => allRenderistList.firstWhere((user) => user.id == elem['value'])).toList();
                  _setState(() {});
                },
              ),
              // SizedBox(
              //   height: 15,
              // ),
              // SizedBox(
              //   width: 400,
              //   child: Row(
              //     children: [
              //       CustomTextFormField(
              //         flex: 1,
              //         label: '',
              //         hintText: "Aggiungi note",
              //         minLines: 4,
              //         controller: renderistNotesController,
              //       ),
              //     ],
              //   ),
              // ),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () async {
                        // String notes = renderistNotesController.text;
                        List<String?> renderistTeamIdList = projectRenderistList.map((user) => user.id).toList();
                        var docRef = FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                            .doc(widget.projectFirebaseId);
                        await docRef.update({
                          'requestStatus': CommonUtils.inLavorazione,
                          'statusChangedDate': DateTime.now().millisecondsSinceEpoch,
                          'renderistTeamIdList': renderistTeamIdList,
                          // 'renderistTeamNotes': notes,
                        }).then((value) {
                          // send request confirmation notification email
                          sendEmail(templateId: CommonUtils.requestConfirmedEmailTemplateId, agency: agency, subject: CommonUtils.requestConfirmedEmailSubject);
                          Fluttertoast.showToast(
                            msg: "Lo stato è cambiato in Confermato!",
                            toastLength: Toast.LENGTH_SHORT,
                            gravity: ToastGravity.BOTTOM,
                            timeInSecForIosWeb: 1,
                            backgroundColor: Colors.green,
                            textColor: Colors.white,
                            fontSize: 16.0,
                          );
                          Navigator.pop(context);
                          widget.updateViewCallback!(
                                          'progetti-attivi');
                          initialFetch();
                        }).catchError((error) {
                          Fluttertoast.showToast(
                            msg: "Errore durante la conferma!",
                            toastLength: Toast.LENGTH_SHORT,
                            gravity: ToastGravity.BOTTOM,
                            timeInSecForIosWeb: 1,
                            backgroundColor: Colors.red,
                            textColor: Colors.white,
                            fontSize: 16.0,
                          );
                          print("Error updating document: $error");
                        });
                      },
                      child: Container(
                        height: 40,
                        width: 210,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(7),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: NarFormLabelWidget(
                            label: "Salva Team",
                            textAlign: TextAlign.center,
                            textColor: AppColor.white,
                            fontWeight: '600',
                            fontSize: 15,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 30,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      });
      },
    );
  }

  Container _characteristics(ImmaginaProject proj) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Caratteristiche",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 16,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Wrap(
              spacing: 20,
              runSpacing: 20,
              children: [
                ...[
                  if (proj.elevator == true) "Ascensore",
                  if (proj.hasCantina == true) "Cantina",
                  if (proj.terrace == true) "Terrazzo",
                  if (proj.hasConcierge == true) "Portineria",
                  if (proj.highEfficiencyFrames == true)
                    "Infissi ad alta efficienza",
                  if (proj.doubleEsposition == true)
                    "Doppia esposizione",
                  if (proj.tripleEsposition == true)
                    "Tripla esposizione",
                  if (proj.quadrupleEsposition == true)
                    "Quadrupla esposizione",
                  if (proj.centralizedHeating == true)
                    "Riscaldamento centralizzato",
                  if (proj.autonomousHeating == true)
                    "Riscaldamento autonomo",
                  if (proj.privateGarden == true) "Giardino privato",
                  if (proj.sharedGarden == true)
                    "Giardino condominiale",
                  if (proj.surveiledBuilding == true)
                    "Stabile videosorvegliato",
                  if (proj.nobleBuilding == true)
                    "Stabile signorile",
                  if (proj.fiber == true) "Fibra ottica",
                  if (proj.airConditioning == true)
                    "Pred. condizionatore",
                  if (proj.securityDoor == true) "Porta blindata",
                  if (proj.tvStation == true) "Impianto TV",
                  if (proj.alarm == true) "Pred. antifurto",
                  if (proj.motorizedSunblind == true)
                    "Tapparelle motorizzate",
                  if (proj.domotizedSunblind == true)
                    "Tapparelle domotizzate",
                  if (proj.domotizedLights == true)
                    "Luci domotizzate",
                  if (proj.highFloor == true) "Piano alto",
                  if (proj.metroVicinity == true) "Vicinanza metro",
                  if (proj.bigBalconies == true) "Ampi balconi",
                  if (proj.bigLiving == true) "Grande zona living",
                  if (proj.doubleBathroom == true) "Doppi servizi",
                  if (proj.hasGarage == true) "Box o garage",
                  if (proj.swimmingPool == true) "Piscina",
                ]
                    .map(
                      (label) => Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            "assets/icons/check_box.svg",
                            height: 14,
                            width: 14,
                          ),
                          SizedBox(width: 10),
                          NarFormLabelWidget(
                            label: label,
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Colors.black,
                          ),
                        ],
                      ),
                    )
                    .toList(),
                ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextFormField(
                        label: "Classe energetica",
                        hintText: proj.energyClass.toString(),
                        fillColor: Color(0xffF5F5F5),
                        flex: 1,
                        isHaveBorder: false,
                        readOnly: true,
                      ),
                      SizedBox(
                        width: 15,
                      ),
                      CustomTextFormField(
                        label: "Anno di costruzione",
                        hintText: proj.constructionYear.toString(),
                        fillColor: Color(0xffF5F5F5),
                        flex: 1,
                        isHaveBorder: false,
                        readOnly: true,
                      ),
                      SizedBox(
                        width: 15,
                      ),
                      Expanded(
                        flex: 2,
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Esposizione',
                                textColor: Color(0xff696969),
                                fontSize: 13,
                                fontWeight: '500',
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: proj.externalEsposition
                                    .map((label) =>
                                        // _externalEspositionIcon(label))
                                      externalEspositionButton(label, proj, context)
                                    )
                                    .toList(),
                              )
                            ]),
                      )
                    ],
                  ),
                ]
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _externalEspositionIcon(String label) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          side: BorderSide(
            color: Colors.transparent,
          ),
          backgroundColor: Color(0xffF5F5F5),
        ),
        onPressed: null,
        child: Text(
          label,
          style: TextStyle(
            color: Theme.of(context).disabledColor,
            fontSize: 14,
            fontFamily: 'Raleway-700',
          ),
        ),
      ),
    );
  }

  Container _description(ImmaginaProject proj) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Descrizione",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: NarFormLabelWidget(
                        fontSize: 14,
                        overflow: TextOverflow.fade,
                        fontWeight: '600',
                        textColor: AppColor.black,
                        label: "${proj.description}",
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Container _motionless(ImmaginaProject proj) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 1.2,
          color: Color(0xffD1D1D1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: "Immobile",
            fontSize: 15,
            fontWeight: '700',
            textColor: Colors.black,
          ),
          SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Tipologia",
                      controller: TextEditingController(
                        text: "${proj.propertyType ?? "None"}",
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Locali",
                      controller: TextEditingController(
                        text: "${proj.rooms}",
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Bagni",
                      controller: TextEditingController(
                        text: "${proj.numberOfBathrooms}",
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Piano",
                      controller: TextEditingController(
                        text: "${proj.unitFloor}",
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Mq",
                      controller: TextEditingController(
                        text: "${proj.grossSquareFootage}",
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 40,
              ),
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      isMoney: true,
                      flex: 0,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Prezzo di vendita",
                      controller: TextEditingController(
                        text: "${proj.listingPrice}",
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Row _header() {
    return Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                widget.projectArguments?.clear();
                widget.projectArguments?.addAll({
                  'isFromRequest': true,
                });
                widget.updateViewCallback!(
                  !(widget.isFromRequest ?? false)
                  ? 'progetti-attivi'
                  : 'richieste',
                    projectArguments: widget.projectArguments);
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                      'assets/icons/arrow_left.svg',
                      height: 15,
                      color: Colors.black),
                  SizedBox(width: 15,),
                  NarFormLabelWidget(
                    label: 'Tutti i progetti',
                    fontSize: 15,
                    fontWeight: '600',
                    textDecoration: TextDecoration.underline,
                    textColor: AppColor.black,
                  ),
                ],
              ),
            ),
          ),

          NarFormLabelWidget(
            label: "${immaginaProject.streetName ?? 'noStreetName'}, ${immaginaProject.streetNumber ?? 'noStreetNum'} ${immaginaProject.housingUnit != null ? '-' : ''} ${immaginaProject.housingUnit ?? ''}",
            fontSize: 20,
            fontWeight: '700',
            textColor: Colors.black,
          ),

          NarFormLabelWidget(
            label: immaginaProject.projectId ?? "",
            fontSize: 12,
            fontWeight: '600',
            textColor: Color(0xFF6C6C6C),
          ),

        ]
    );
  }
}
