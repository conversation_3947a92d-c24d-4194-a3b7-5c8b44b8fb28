import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:newarc_platform/classes/projectJob.dart';
import 'package:newarc_platform/utils/const.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom-button.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;

showAlertDialog(BuildContext context, String title, String message,
    {String? buttonText, bool addCancel = false}) async {
  // show the dialog
  var returnVariable = await showDialog(
    context: context,
    builder: (BuildContext context) {
      //return alert;
      return Center(
          child: BaseNewarcPopup(
        title: title,
        column: Text(
          textAlign: TextAlign.center,
          message,
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        buttonText: buttonText,
      ));
    },
  );

  if (returnVariable == null) {
    return false;
  } else
    return returnVariable;
}

showToastNotification(BuildContext context, String message, {int duration = 2,bool error=false,}) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(
        message,
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
      ),
      backgroundColor: error ? Colors.red : Colors.green,
      duration: Duration(seconds: duration),
    ),
  );
}
String getFormattedDate(int? millisecondsSinceEpoch) {
  if (millisecondsSinceEpoch == null) {
    return "Da definire";
  }
  final date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  final formatter = DateFormat("dd/MM/yyyy");
  return formatter.format(date);
}

String valuatorSubmissionsCollection() {
  if (kDebugMode) {
    return 'valuatorSumbissions';
  } else {
    return 'valuatorSumbissionsTest';
  }
}

// <EMAIL>
// nishu@1989
customDialogWithActions(BuildContext context, String header_title,
    String description, List<Widget> actionButtons) {
  if (actionButtons.length == 0) {
    actionButtons = [
      ui_button(context, 'OK', () async {
        Navigator.of(context).pop();
      }, 'primary', 'md', null)
    ];
  }

  showDialog<void>(
    context: context,
    barrierDismissible: false, // user must tap button!
    builder: (BuildContext context) {
      return AlertDialog(
        content: Builder(
          builder: (context) {
            return BaseNewarcPopup(
              title: header_title,
              column: Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color.fromARGB(255, 73, 73, 73),
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
              ),
            );
          },
        ),
      );
    },
  );
}

String kappafyPrice(double priceInThousands) {
  try {
    return '€' + (priceInThousands / 100).toStringAsFixed(0) + "k";
  } catch (e) {
    return "error";
  }
}

String formatPrice(double priceInThousands, {int digits = 0}) {
  try {
    NumberFormat formatter = NumberFormat.decimalPattern("it_IT");

    String formattedNumber = formatter.format(priceInThousands);
    return '€' + formattedNumber; //+ priceInThousands.toStringAsFixed(digits);
  } catch (e) {
    return "error";
  }
}

extension StringCasingExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String toTitleCase() => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized())
      .join(' ');
}

bool isToday(int millisecondsSinceEpoch) {
  DateTime givenDate = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  DateTime now = DateTime.now();

  return givenDate.year == now.year &&
         givenDate.month == now.month &&
         givenDate.day == now.day;
}

extension EmailValidator on String {
  bool isValidEmail() {
    return RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(this);
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

String capitalize(String value) {
  if (value.trim().isEmpty) return "";
  return "${value[0].toUpperCase()}${value.substring(1).toLowerCase()}";
}

bool isNumber(dynamic value) {
  return value is num || (value is String && double.tryParse(value) != null);
  try {
    if (value is num) {
      return true;
    } else if (value is String && double.tryParse(value) != null) {
      return true;
    } else if (value is String) {
      value = value.replaceAll('.', '');
      if (double.tryParse(value) != null) return true;
      if (value.endsWith(',')) {
        if (double.tryParse(value.replaceAll(',', '.0')) != null) return true;
      } else {
        if (double.tryParse(value.replaceAll(',', '.')) != null) return true;
      }
      return false;
    } else {
      return false;
    }
  } catch (e) {
    print("VALIDA");

    print(value);
    return false;
  }

  //return value is num || (value is String && double.tryParse(value) != null);
}

String generateRandomString(int len) {
  var r = Random();
  const _chars =
      'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
  return List.generate(len, (index) => _chars[r.nextInt(_chars.length)]).join();
}

String convertToCamelCase(String str) {
  // Split the input string by spaces
  List<String> words = str.split(' ');

  // Convert the first word to lowercase
  String camelCaseString = words[0].toLowerCase();

  // Convert the first letter of each subsequent word to uppercase and concatenate
  for (int i = 1; i < words.length; i++) {
    String word = words[i];
    camelCaseString += word[0].toUpperCase() + word.substring(1).toLowerCase();
  }

  return camelCaseString;
}

String convertFromCamelCase(String str) {
  List<String> words = [];
  StringBuffer currentWord = StringBuffer();

  for (int i = 0; i < str.length; i++) {
    if (i > 0 &&
        str[i].toUpperCase() == str[i] &&
        str[i].toLowerCase() != str[i]) {
      // Found an uppercase letter indicating the start of a new word
      words.add(currentWord.toString());
      currentWord = StringBuffer();
    }
    currentWord.write(str[i]);
  }
  // Add the last word
  words.add(currentWord.toString());

  // Join words with a space and capitalize the first word
  String result = words.map((word) {
    return word.toLowerCase();
  }).join(' ');

  // Capitalize the first letter of the first word
  return result[0].toUpperCase() + result.substring(1);
}

double calculateProjectCompletionPercentage(List<ProjectJob>? projectJobs) {
  double total = 1;
  double completedPercentage = 1;
  if (projectJobs != null && projectJobs.isNotEmpty) {
    projectJobs.forEach((projectJob) {

      Map<String, dynamic> activityMap = projectJobsActivities.firstWhere(
          (item) => (item['value']??'').toString().toLowerCase() == (projectJob.activity??'').toString().toLowerCase(),
          orElse: () => {'timelineValue': 0});

      if (activityMap['timelineValue'] != null) {
        total += (activityMap['timelineValue'] ?? 0) as num;
      }
      // total += activityMap['timelineValue'];
      if (projectJob.status == '2 Completati') {
        completedPercentage += (activityMap['timelineValue'] ?? 0) as num;
      }
    });
  }

  if (completedPercentage == 1 && total == 1) {
    return 0;
  } else {
    return completedPercentage / total;
  }
  
  
}

List<String> getJobsInProgress(List<ProjectJob>? projectJobs) {
  List<String> jobsInProgress = [];

  if (projectJobs!.length > 0 ) {
    
    projectJobs.forEach((projectJob) {
      print(projectJob.toMap());
      Map<String, dynamic> activityMap = projectJobsActivities
          .firstWhere((item) => item['value'] == projectJob.activity, orElse: () => {'timelineValue': 0} );

      if (projectJob.status == '1 In corso' ) {
        jobsInProgress.add(activityMap['value']);
      }
    });
  }
  return jobsInProgress;
}

String timestampToUtcDate(int millisecondsSinceEpoch) {
  if (millisecondsSinceEpoch == 0) return '';

  return DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
          .day
          .toString() +
      '/' +
      DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
          .month
          .toString() +
      '/' +
      DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
          .year
          .toString();
}

List<double> getCustomItemsHeights(items, height) {
  final List<double> itemsHeights = [];
  for (int i = 0; i < (items.length * 2) - 1; i++) {
    if (i.isEven) {
      itemsHeights.add(height);
    }
    //Dividers indexes will be the odd indexes
    if (i.isOdd) {
      itemsHeights.add(4);
    }
  }
  return itemsHeights;
}

getProjectActivities() async {

  List _activity = [];
  final FirebaseFirestore _db = FirebaseFirestore.instance;
  QuerySnapshot<Map<String, dynamic>> snapshot = await _db
      .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
      .get();

  if( snapshot.docs.length > 0 ) {
        
    _activity.add({'value': '', 'label': '', 'category': '', 'timelineValue': 0});
    
    for (var i = 0; i < snapshot.docs.length; i++) {
      // List<Map<String, dynamic>> existingActivities = 

      if( snapshot.docs[i].data()['activities'] != null ) {
        (snapshot.docs[i].data()['activities'] as List<dynamic>?)
          ?.map((acti) {
            Map<String, dynamic>.from(acti);
            _activity.add(
            {
              'value': acti['activity'], 
              'label': acti['activity'], 
              'category': snapshot.docs[i].data()['categoryName'], 
              'timelineValue': int.tryParse(acti['timeline'])
            });    
          }).toList();    
      }
      
    }
  }

  return _activity;

}

Map<String, Map<String, dynamic>> deepCopy(Map<String, Map<String, dynamic>> original) {
  return original.map((key, value) => MapEntry(
        key,
        {
          'status': value['status'],
          'children': Map<String, dynamic>.from(value['children']),
        },
      ));
}

Map<String, dynamic> getDefaultMenuAccessByRole(role) {

  switch (role) {
    case 'finance': case 'master': case 'administration':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;

    case 'renderist':
      
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';
      return appConst.allUserRoleMenuControls[role]!;

    case 'geometra':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';
      return appConst.allUserRoleMenuControls[role]!;
    
    case 'media_creator':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'hide';

      return appConst.allUserRoleMenuControls[role]!;
    
    case 'scouter':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;
    
    case 'renovator':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'hide';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;
    
    case 'administration':
      appConst.allUserRoleMenuControls[role]!['progetti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ristrutturazioni']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['contatti']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['gestione']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['ads-manager']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['immagina']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['forniture']['status'] = 'show';
      appConst.allUserRoleMenuControls[role]!['materioteca']['status'] = 'show';

      return appConst.allUserRoleMenuControls[role]!;
      
      
    default:
      return appConst.allUserRoleMenuControls[role]!;
  }

}

Map<String, dynamic> getDefaultProjectTabByRole(role) {

  switch (role) {
    
    case 'renovator':
      
      return {
        "generali": {
          'status': 'show',
          'children': {
            'Dashboard': 'show',
            'Immobile': 'show',
            'Team Newarc': 'show',
          }
        },
        "acquisizione/rivendita": {
          'status': 'show',
          'children': {
            'Agenzia': 'show',
            'Annuncio': 'show'
          }
        },
        "ristrutturazione": {
          'status': 'show',
          'children': {
            'Gestisci Lavori': 'show',
            'Gestisci Materiali': 'show',
            'Aggiornamenti cantiere': 'show',
          }
        },
        "economics": {
          'status': 'hide',
          'children': {
            'Pagamenti': 'hide',
            'Conto Economico': 'hide'
          }
        },
        "file e documenti": {
          'status': 'show',
          'children': {
            'Atti e Contratti': 'show',
            'Progettazione': 'show',
            'Impiantistica': 'show',
            'Planimetrie': 'show',
            'Render': 'show',
            'Tour virtuale': 'show',
            'Pratiche Edilizie': 'show',
            'Brochure': 'show',
            'Foto': 'show',
            'Video': 'show'
          }
        }
      };

    case 'renderist' : case 'media_creator':
      return {
        "generali": {
          'status': 'hide',
          'children': {
            'Dashboard': 'hide',
            'Immobile': 'hide',
            'Team Newarc': 'hide',
          }
        },
        "acquisizione/rivendita": {
          'status': 'hide',
          'children': {
            'Agenzia': 'hide',
            'Annuncio': 'hide'
          }
        },
        "ristrutturazione": {
          'status': 'hide',
          'children': {
            'Gestisci Lavori': 'hide',
            'Gestisci Materiali': 'hide',
            'Aggiornamenti cantiere': 'hide',
          }
        },
        "economics": {
          'status': 'hide',
          'children': {
            'Pagamenti': 'hide',
            'Conto Economico': 'hide'
          }
        },
        "file e documenti": {
          'status': 'show',
          'children': {
            'Atti e Contratti': 'show',
            'Progettazione': 'show',
            'Impiantistica': 'show',
            'Planimetrie': 'show',
            'Render': 'show',
            'Tour virtuale': 'show',
            'Pratiche Edilizie': 'show',
            'Brochure': 'show',
            'Foto': 'show',
            'Video': 'show'
          }
        }
      };
    
    
    default:
      return {
        "generali": {
          'status': 'show',
          'children': {
            'Dashboard': 'show',
            'Immobile': 'show',
            'Team Newarc': 'show',
          }
        },
        "acquisizione/rivendita": {
          'status': 'show',
          'children': {
            'Agenzia': 'show',
            'Annuncio': 'show'
          }
        },
        "ristrutturazione": {
          'status': 'show',
          'children': {
            'Gestisci Lavori': 'show',
            'Gestisci Materiali': 'show',
            'Aggiornamenti cantiere': 'show',
          }
        },
        "economics": {
          'status': 'show',
          'children': {
            'Pagamenti': 'show',
            'Conto Economico': 'show'
          }
        },
        "file e documenti": {
          'status': 'show',
          'children': {
            'Atti e Contratti': 'show',
            'Progettazione': 'show',
            'Impiantistica': 'show',
            'Planimetrie': 'show',
            'Render': 'show',
            'Tour virtuale': 'show',
            'Pratiche Edilizie': 'show',
            'Brochure': 'show',
            'Foto': 'show',
            'Video': 'show'
          }
        }
      };
  }

}

int roundToNearestThousand(double value) {
  return (value / 1000).round() * 1000;
}

Widget externalEspositionButton(String label, ImmaginaProject project, context, {stateSetter= null}) {
  return Padding(
    padding: const EdgeInsets.only(right: 8.0),
    child: OutlinedButton(
      style: OutlinedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        side: BorderSide(
            color: project.externalEsposition.contains(label)? Colors.transparent : Color(0xffdbdbdb)
        ),
        backgroundColor: project.externalEsposition.contains(label)? Theme.of(context).primaryColor : Colors.transparent,
      ),
      onPressed: stateSetter == null 
      ? null
      : () {
        if (project.externalEsposition.contains(label)) {
          stateSetter(() {
            project.externalEsposition.removeWhere((item) => item == label);
          });
        } else {
          stateSetter(() {
            project.externalEsposition.add(label);
          });
        }
      },
      child: Text(
        label,
        style: TextStyle(
          color: project.externalEsposition.contains(label) ? Theme.of(context).unselectedWidgetColor : Theme.of(context).primaryColorDark,
          fontSize: 14,
          fontFamily: 'Raleway-600',
          fontWeight: FontWeight.w600,
        ),
      ),
    ),
  );
}