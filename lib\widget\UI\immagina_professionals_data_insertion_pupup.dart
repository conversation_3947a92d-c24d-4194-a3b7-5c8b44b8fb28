import 'dart:math';
import 'dart:typed_data';
import 'dart:html' as html;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/main.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/dropdown_search.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/checkbox.dart';
import 'package:newarc_platform/widget/UI/bar_toggle_button.dart';
import 'package:newarc_platform/widget/UI/si_no_button.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/economics.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as p;
import '../../utils/common_utils.dart';
import '../../utils/heicToJpegConverter.dart' as heicToJpeg;

class ImmaginaProfessionalsDataInsertionPopup extends StatefulWidget {
  final String procedureStep;
  final ImmaginaProject? project;
  final List<String> formErrorMessage;
  final AgencyUser agencyUser;
  final onClose;

  // final bool isStripPayOnly;

  ImmaginaProfessionalsDataInsertionPopup({
    required this.procedureStep,
    this.project,
    this.formErrorMessage = const [],
    required this.agencyUser,
    required this.onClose,
    Key? key,
    // required this.isStripPayOnly,
  }) : super(key: key) {
    if (project == null && procedureStep != 'initial') {
      throw ArgumentError(
        'If project is null, procedureStep must be "initial".',
      );
    }
  }

  @override
  State<ImmaginaProfessionalsDataInsertionPopup> createState() => _ImmaginaProfessionalsDataInsertionPopupState();
}

class _ImmaginaProfessionalsDataInsertionPopupState extends State<ImmaginaProfessionalsDataInsertionPopup> {
  TextStyle sectionTitleStyle = TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool paymentError = false;
  bool clicked = false;
  List<String> formErrorMessage = [];
  ImagePicker picker = ImagePicker();

  List viewsOrder = const [
    'initial',
    'localizzazione',
    'info-generali',
    'descrizione',
    'caratteristiche',
    'planimetria',
    'materiali',
    'indicazioni-speciali',
    'optionals',
    'pagamento',
  ];

  Map<String, String> _errorMessage = {
    'localizzazione': 'Inserisci tutti i campi richiesti prima di procedere',
    'info-generali': 'Inserisci tutti i campi richiesti prima di procedere',
    'descrizione': 'Inserisci una descrizione del tuo immobile prima di procedere',
    'caratteristiche': 'Inserisci le caratteristiche richieste prima di procedere.\nClasse energetica, anno di costruzione, esposizione e almeno uno dei campi selezionabili sono obbligatori.',
    'planimetria': 'Fornisci le tue planimetrie o richiedi il servizio a Newarc',
    'materiali': 'Inserisci tutti i campi richiesti prima di procedere',
    'optionals': 'Inserisci tutti i campi richiesti prima di procedere',
    'indicazioni-speciali': 'Se necessario, inserire le informazioni aggiuntive. \nRicontrolla che tutte le sezioni siano complete prima di procedere con la pagina di pagamento.\nPuoi verificare le sezioni incomplete chiudendo il popup Nuova richiesta progetto dopo aver salvato. ',
  };

  List<String> allowedPlanimetryExtensions = ['dwg', 'pdf', 'jpg', 'jpeg'];

  Map<String, List<String>> cityMarketZoneMap = appConst.cityZones;

  Map<int, String> localiMap = {
    1: "Monolocale",
    2: "Bilocale",
    3: "Trilocale",
    4: "Quadrilocale",
    5: "Plurilocale",
  };

  // Popup main state variables
  String? selectedView;
  ImmaginaProject? selectedProject;

  // Pagamento state variable
  bool _isFrozen = false;

  // Localizzazione
  TextEditingController filterMarketZone = TextEditingController();

  // Info generali
  List<Map<String, TextEditingController>> infoGeneraliTaglioControllerList = [];

  // Descrizione
  TextEditingController filterDescription = TextEditingController();

  // Caratteristiche
  List<Map<String, TextEditingController>> caratteristicheTaglioControllerList = [];

  // Materiali
  List<TextEditingController> materialiTaglioControllerList = [];

  // Indicazioni Speciali
  TextEditingController filterSpecialHints = TextEditingController();
  TextEditingController filterPropertyUpForSaleAnswer = TextEditingController();

  void initializeControllers() {
    infoGeneraliTaglioControllerList.clear();
    caratteristicheTaglioControllerList.clear();
    materialiTaglioControllerList.clear();
    // Localizzazione
    filterMarketZone.text = selectedProject!.marketZone ?? "";
    // Tagli controllers
    selectedProject!.childrenProjects!.forEach((child) {
      TextEditingController filterPropertyType = new TextEditingController();
      filterPropertyType.text = child.propertyType ?? "";
      TextEditingController filterRooms = new TextEditingController(
        text: child.rooms != null 
          ? child.rooms == 5
            ? "5+"
            : child.rooms.toString() 
          : "");
      TextEditingController filterBathrooms = new TextEditingController(
        text: child.numberOfBathrooms != null 
          ? child.numberOfBathrooms == 3
            ? "3+"
            : child.numberOfBathrooms.toString()
          : "");
      TextEditingController filterUnitFloor = new TextEditingController(
        text: child.unitFloor != null 
          ? child.unitFloor == 10
            ? "10+"
            : child.unitFloor.toString()
          : "");
      TextEditingController filterGSF = new TextEditingController();
      filterGSF.text = child.grossSquareFootage != null ? child.grossSquareFootage.toString() : "";
      TextEditingController filterListingPrice = new TextEditingController(
        text: child.listingPrice != null
          ? NumberFormat('#,##0', 'it_IT').format(child.listingPrice!)
          : ""
      );

      infoGeneraliTaglioControllerList.add({
        'propertyType': filterPropertyType,
        'rooms': filterRooms,
        'bathrooms': filterBathrooms,
        'unitFloor': filterUnitFloor,
        'GSF': filterGSF,
        'listingPrice': filterListingPrice,
      });

      TextEditingController filterEnergyClass = TextEditingController(text: child.energyClass ?? "");
      TextEditingController filterConstructionYear = TextEditingController(text: child.constructionYear != null ? child.constructionYear.toString() : "");

      caratteristicheTaglioControllerList.add({
        'energyClass': filterEnergyClass,
        'constructionYear': filterConstructionYear,
      });

      materialiTaglioControllerList.add(TextEditingController(text: child.materialsNotes ?? ""));
    });
    // Descrizione
    filterDescription.text = selectedProject!.description ?? "";
    // Indicazioni Speciali
    filterSpecialHints.text = selectedProject!.specialHints ?? "";
    filterPropertyUpForSaleAnswer.text = selectedProject!.propertyUpForSaleAnswer ?? "";
  }

  @override
  void initState() {
    cityMarketZoneMap.forEach((key, value) {
      value.sort();
      value.map((zone)=>zone.toTitleCase());
      });
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
    super.initState();
    if (selectedProject != null) {
      initializeControllers();
    }
  }

  @override
  void didUpdateWidget(ImmaginaProfessionalsDataInsertionPopup oldWidget) {
    super.didUpdateWidget(oldWidget);
    formErrorMessage = widget.formErrorMessage;
    selectedView = widget.procedureStep;
    selectedProject = widget.project;
  }

  Widget selectFooter() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialFooter();
    } else {
      return standardFooter();
    }
  }

  Widget initialFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          width: 150,
          child: BaseNewarcButton(
            buttonText: 'Inizia',
            color: Colors.black,
            onPressed: () async {
              // create immagina project object
              ImmaginaProject _project = ImmaginaProject.empty();
              _project.isForProfessionals = true;
              _project.agencyId = widget.agencyUser.agencyId!;
              _project.insertionTimestamp = DateTime.now().millisecondsSinceEpoch;
              _project.childrenProjects = [];
              ImmaginaProject child = ImmaginaProject.empty();
              child.id = generateRandomString(13);
              child.agencyId = _project.agencyId;
              child.insertionTimestamp = _project.insertionTimestamp;
              _project.childrenProjects!.add(child);
              // save project to firestore
              DocumentReference projectResponse = await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).add(_project.toMap());
              _project.id = projectResponse.id;
              setState(() {
                selectedView = 'localizzazione';
                selectedProject = _project;
                initializeControllers();
              });
            }
          ),
        )
      ],
    );
  }

  Widget standardFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        selectedView == 'localizzazione'
            ? SizedBox(width: 150)
            : ElevatedButton(
            onPressed: () => _backButtonSetStateFunction(),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              fixedSize: Size(150, 45),
              backgroundColor: Color(0xffe8e8e8),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Transform.rotate(
                angle: pi,
                child: SvgPicture.asset(
                  'assets/icons/arrow.svg',
                  height: 16,
                  color: const Color(0xff6a6a6a),
                ),
              ),
              NarFormLabelWidget(
                label: 'Indietro',
                textColor: const Color(0xff6a6a6a),
                fontWeight: '600',
                letterSpacing: .5,
              ),
            ])),
        selectedView == 'pagamento'
            ? SizedBox(width: 150)
            : ElevatedButton(
            onPressed: () => _forwardButtonConditions() ? _forwardButtonSetStateFunction() : _forwardButtonErrorFunction(),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              fixedSize: Size(150, 45),
              backgroundColor: _forwardButtonConditions() ? Colors.black : Color.fromARGB(255, 192, 192, 192),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              NarFormLabelWidget(
                label: 'Avanti',
                textColor: Colors.white,
                fontWeight: '600',
                letterSpacing: .5,
              ),
              SvgPicture.asset(
                'assets/icons/arrow.svg',
                height: 16,
                color: Colors.white,
              ),
            ]
          )
        ),
      ],
    );
  }

  bool _forwardButtonConditions() {  
    Map conditions = {
      'localizzazione': selectedProject!.localizzazione(),
      'info-generali': (selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty) 
        ? selectedProject!.childrenProjects?.every((child) => child.info_generali()) 
        : false,
      'descrizione': (selectedProject!.description != null) && (selectedProject!.description != ""),
      'caratteristiche': (selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty) 
        ? selectedProject!.childrenProjects?.every((child) => child.checkDotazioni()) 
        : false,
      'planimetria': (selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty) 
        ? selectedProject!.childrenProjects?.every((child) => child.planimetry.isNotEmpty) 
        : false,
      'materiali': selectedProject!.wantsMaterialNotes
        ? (selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty)
          ? selectedProject!.childrenProjects?.every((child) => child.materialsNotes != null && child.materialsNotes != "") 
          : false
        : true,
      'optionals': true
    };
    bool indicazioniSpecialiCondition = (((selectedProject!.hasSpecialHints == null) || (selectedProject!.hasSpecialHints == false)) && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")) ||
          ((selectedProject!.hasSpecialHints == true) && (selectedProject!.specialHints != null) && (selectedProject!.specialHints != "") && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != ""));
    conditions["indicazioni-speciali"] = conditions.values.every((value) => value == true) && indicazioniSpecialiCondition;
    bool condition = conditions[selectedView] ?? false;
    return condition;
  }

  _backButtonSetStateFunction() {
    if (selectedView != 'localizzazione') {
      setState(() {
        selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) - 1];
      });
    }
  }

  _forwardButtonErrorFunction() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
            title: 'Errore',
            buttonColor: Colors.black,
            column: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                NarFormLabelWidget(
                  label: _errorMessage[selectedView],
                  textAlign: TextAlign.center,
                  fontWeight: '600',
                  letterSpacing: 1,
                ),
              ],
            ),
          )
        );
      }
    );
  }

  _forwardButtonSetStateFunction() async {
    setState(() {
      _isFrozen = true;
    });
    String? currentView = selectedView;
    if (currentView == 'localizzazione') {
      if (selectedProject!.localizzazione()) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'info-generali') {
      if (selectedProject!.childrenProjects?.every((child) => child.info_generali()) ?? false) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
        // if (selectedProject!.grossSquareFootage!<gSFUpperLimit){
        //   setState(() {
        //     selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        //   });
        // } else {
        //   showDialog(
        //     context: context,
        //     barrierDismissible: false,
        //     builder: (_) {
        //       return Center(
        //         child: BaseNewarcPopup(
        //           title: 'Attenzione!',
        //           noButton: true,
        //           column: Container(
        //             width: MediaQuery.of(context).size.width / 3,
        //             child: Column(
        //               mainAxisAlignment: MainAxisAlignment.center,
        //               crossAxisAlignment: CrossAxisAlignment.center,
        //               children: [
        //                 NarFormLabelWidget(
        //                   label: 'Stai effettuando una richiesta per una\ncasa di oltre ${gSFUpperLimit}mq!',
        //                   textAlign: TextAlign.center,
        //                   fontSize: 20,
        //                   fontWeight: '700',
        //                 ),
        //                 SizedBox(height: 30),
        //                 NarFormLabelWidget(
        //                   label: 'Newarc realizzerà un preventivo personalizzato per questo immobile e non sarà possibile utilizzare i crediti di un piano ad abbonamento.',
        //                   textAlign: TextAlign.center,
        //                   fontSize: 15,
        //                   fontWeight: '500',
        //                   textColor: Color(0xff696969),
        //                   overflow: TextOverflow.clip,
        //                 ),
        //                 SizedBox(height: 20),
        //                 NarFormLabelWidget(
        //                   label: 'Il preventivo comprenderà la cifra per la realizzazione del progetto Immagina + la Success Fee',
        //                   textAlign: TextAlign.center,
        //                   fontSize: 15,
        //                   fontWeight: '500',
        //                   textColor: Color(0xff696969),
        //                   overflow: TextOverflow.clip,
        //                 ),
        //                 SizedBox(height: 30),
        //                 BaseNewarcButton(
        //                   width: MediaQuery.of(context).size.width/6,
        //                   buttonText: 'Continua',
        //                   onPressed: () {
        //                     Navigator.of(context).pop();
        //                     setState(() {
        //                       selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        //                     });
        //                   },
        //                 ),
        //                 SizedBox(height: 10),
        //                 BaseNewarcButton(
        //                   buttonText: 'Salva e chiudi',
        //                   width: MediaQuery.of(context).size.width/6,
        //                   textColor: Theme.of(context).primaryColor,
        //                   color: Colors.white,
        //                   onPressed: () {
        //                       Navigator.of(context).pop();
        //                       Navigator.of(context).pop();
        //                       widget.onClose();
        //                     },
        //                 ),
        //               ],
        //             ),
        //           ),
        //         ),
        //       );
        //     },
        //   );
        // }  
      }
    } else if (currentView == 'descrizione') {
      if ((selectedProject!.description != null) && (selectedProject!.description != "")) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'caratteristiche') {
      if ((selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty) 
        ? selectedProject!.childrenProjects!.every((child) => child.checkDotazioni()) 
        : false) {
        // saveCharacteristics();
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'planimetria') {
      if ((selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty) 
        ? selectedProject!.childrenProjects!.every((child) => child.planimetry.isNotEmpty) 
        : false) {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'materiali') {
      if (selectedProject!.wantsMaterialNotes) {
        if ((selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty) 
          ? selectedProject!.childrenProjects!.every((child) => child.materialsNotes != null && child.materialsNotes != "") 
          : false) {
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
          });
        }
      } else {
        setState(() {
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    } else if (currentView == 'optionals') {
      setState(() {
        selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
      });
    } else if (currentView == 'indicazioni-speciali') {
      if ((selectedProject!.hasSpecialHints ?? false) && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")) {
        if ((selectedProject!.specialHints != null) && (selectedProject!.specialHints != "") && (selectedProject!.propertyUpForSaleAnswer != null) && (selectedProject!.propertyUpForSaleAnswer != "")) {
          setState(() {
            selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
          });
        }
      } else {
        setState(() {
          selectedProject!.hasSpecialHints = false;
          selectedView = viewsOrder[viewsOrder.indexWhere((elem) => elem == selectedView) + 1];
        });
      }
    }
    await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
    setState(() {
      _isFrozen = false;
    });
  }

  Future<String> computeImmaginaProjectCode () async { 
    // create and assign internal code (projectId) for immaginaProject
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    String _region = selectedProject!.addressInfo!.region!;
    String _regionCode = appConst.composeProjectCode['region']![_region]!;
    String _serviceCode = appConst.composeProjectCode['project']!['immagina']!;
    DateTime _createdDate = DateTime.fromMillisecondsSinceEpoch(selectedProject!.insertionTimestamp);
    String _yearCode = _createdDate.year.toString().substring(2); // Get last two digits of the year
    String _defaultProjectId = "P_${_serviceCode}${_regionCode}${_yearCode}0001";

    QuerySnapshot<Map<String, dynamic>> _immaginaProjectsRegionCollection = await _db
      .collection(appConfig.COLLECT_IMMAGINA_PROJECTS).where('region', isEqualTo: _region)
      .orderBy('insertionTimestamp', descending: true)
      .limit(1).get();
      
    if (_immaginaProjectsRegionCollection.docs.isNotEmpty) {
      Map<String, dynamic> lastDoc = _immaginaProjectsRegionCollection.docs[0].data();
      if (DateTime.fromMillisecondsSinceEpoch(lastDoc['insertionTimestamp']).year == DateTime.now().year) {
        RegExp matcher = RegExp(r"\d{4}$");
        final match = matcher.firstMatch(lastDoc['projectId']);
        if (match != null) {
          final matchedText = match.group(0);
          final seqNumber = int.parse(matchedText!) + 1;
          final stringSeqNumber = seqNumber.toString();
          final paddedStringSeqNumber = stringSeqNumber.padLeft(4, '0');
          _defaultProjectId =
          "P_${appConst.composeProjectCode['project']!['immagina']}${appConst.composeProjectCode['region']![_region]}${(DateTime.now().year) % 100}${paddedStringSeqNumber}";
        }
      }
    }
    return _defaultProjectId;
  }

  void saveCharacteristics(ImmaginaProject project, Map characteristics) {
    project.elevator = characteristics['Ascensore'];
    project.hasCantina = characteristics['Cantina'];
    project.terrace = characteristics['Terrazzo'];
    project.hasConcierge = characteristics['Portineria'];
    project.highEfficiencyFrames = characteristics['Infissi ad alta efficienza'];
    project.doubleEsposition = characteristics['Doppia esposizione'];
    project.tripleEsposition = characteristics['Tripla esposizione'];
    project.quadrupleEsposition = characteristics['Quadrupla esposizione'];
    project.centralizedHeating = characteristics['Risc. centralizzato'];
    project.autonomousHeating = characteristics['Risc. autonomo'];
    project.privateGarden = characteristics['Giardino privato'];
    project.sharedGarden = characteristics['Giardino condominiale'];
    project.surveiledBuilding = characteristics['Stabile videosorvegliato'];
    project.nobleBuilding = characteristics['Stabile signorile'];
    project.fiber = characteristics['Fibra ottica'];
    project.airConditioning = characteristics['Pred. condizionatore'];
    project.securityDoor = characteristics['Porta blindata'];
    project.tvStation = characteristics['Impianto TV'];
    project.alarm = characteristics['Pred. antifurto'];
    project.motorizedSunblind = characteristics['Tapparelle motorizzate'];
    project.domotizedSunblind = characteristics['Tapparelle domotizzate'];
    project.domotizedLights = characteristics['Luci domotizzate'];
    project.highFloor = characteristics['Piano alto'];
    project.metroVicinity = characteristics['Vicinanza Metro'];
    project.bigBalconies = characteristics['Ampi balconi'];
    project.bigLiving = characteristics['Grande zona living'];
    project.doubleBathroom = characteristics['Doppi servizi'];
    project.swimmingPool = characteristics['Piscina'];
    project.hasGarage = characteristics['Box o garage'];
    project.solarPanel = characteristics['Fotovoltaico'];
    project.walkInCloset = characteristics['Cabina armadio'];
  }

  Future savePlanimetryImages(ImmaginaProject childProject, List<XFile> planimetryImages) async {
    String path = '${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/tagli/${childProject.id}/';
    Reference ref = FirebaseStorage.instance.ref(path);
    final listResult = await ref.listAll();
    for (var element in listResult.items) {
      await FirebaseStorage.instance.ref(element.fullPath).delete();
    }
    childProject.planimetry.clear();
    for (int i = 0; i < planimetryImages.length; i++) {
      String pictureFilename = 'planimetry_${i + 1}' + p.extension(planimetryImages[i].name);
      await uploadImageToStorage(path, pictureFilename, planimetryImages[i]);
      childProject.planimetry.add(pictureFilename);
    }
    await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
  }

  Future<UploadTask?> uploadImageToStorage(String directory, String filename, XFile? file) async {
    if (file == null) {
      return null;
    }
    UploadTask uploadTask;
    Reference ref = FirebaseStorage.instance.ref(directory).child(filename);

    final metadata = SettableMetadata(
      contentType: file.mimeType,
      customMetadata: {'picked-file-path': file.path},
    );

    uploadTask = ref.putData(await file.readAsBytes(), metadata);
    uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
      switch (taskSnapshot.state) {
        case TaskState.running:
          final progress = 100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
          // print("Upload is $progress% complete.");
          break;
        case TaskState.paused:
        // print("Upload is paused.");
          break;
        case TaskState.canceled:
        // print("Upload was canceled");
          break;
        case TaskState.error:
        // Handle unsuccessful uploads
          break;
        case TaskState.success:
        // Handle successful uploads on complete
        // ...
          break;
      }
    });

    return Future.value(uploadTask);
  }

  Future<List<XFile>> getFirestoreImages(ImmaginaProject childProject) async {
  List<XFile> planimetryImages = [];

  if (selectedProject != null && childProject.planimetry.isNotEmpty) {
    String directory = '${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/tagli/${childProject.id}/';
    
    for (String plan in childProject.planimetry) {
      try {
        Reference ref = FirebaseStorage.instance.ref().child(directory + plan);
        Uint8List? data = await ref.getData();
        if (data != null) {
          planimetryImages.add(XFile.fromData(data, name: plan));
        }
      } catch (e) {
        print('Error loading planimetry $plan: $e');
        // Optionally continue with other images or handle error
      }
    }
  }
  
  return planimetryImages;
}

  selectView() {
    if (selectedView == null) {
      return NarFormLabelWidget(label: "view can't be null selected");
    } else if (selectedView == 'initial') {
      return initialDialog();
    } else if (selectedView == 'localizzazione') {
      return localizzazioneDialog();
    } else if (selectedView == 'info-generali') {
      return infoGeneraliDialog();
    } else if (selectedView == 'descrizione') {
      return descrizioneDialog();
    } else if (selectedView == 'caratteristiche') {
      return caratteristicheDialog();
    } else if (selectedView == 'planimetria') {
      return planimetriaDialog();
    } else if (selectedView == 'materiali') {
      return materialiDialog();
    } else if (selectedView == 'indicazioni-speciali') {
      return indicazioniSpecialiDialog();
    } else if (selectedView == 'optionals') {
      return optionalsDialog();
    } else if (selectedView == 'pagamento') {
      return pagamentoDialog();
    } else {
      return Center(child: NarFormLabelWidget(label: "view $selectedView unknown"));
    }
  }

  Widget initialDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Image.asset(
          'assets/logo_newarc_immagina_professionals.png',
          height: 50,
        ),
        NarFormLabelWidget(
          label: 'Sei pronto per effettuare \nuna nuova richiesta di progetto?',
          overflow: TextOverflow.visible,
          textAlign: TextAlign.center,
          fontWeight: '800',
          fontSize: 30,
          textColor: Theme.of(context).disabledColor,
        ),
        Container(
          height: MediaQuery.of(context).size.height / 3.5,
          width: 600,
          decoration: BoxDecoration(color: const Color.fromARGB(255, 234, 234, 234), borderRadius: BorderRadius.all(Radius.circular(15))),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 10,
              ),
              NarFormLabelWidget(
                label: 'COSA TI SERVIRÀ',
                fontWeight: '700',
                fontSize: 14,
                textColor: Color(0xff5f5f5f),
                letterSpacing: 2,
              ),
              SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/dati-immobile.png',
                          color: Color(0xFF737373),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Dati immobile',
                          fontWeight: '800',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: '',
                          fontWeight: '800',
                          fontSize: 10,
                          textColor: Theme.of(context).primaryColorLight,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/planimetria.png',
                          color: Color(0xFF737373),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Planimetria di progetto',
                          fontWeight: '800',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: '',
                          fontWeight: '800',
                          fontSize: 10,
                          textColor: Color(0xff878787),
                          letterSpacing: 1,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/icons/pagamento.png',
                          color: Color(0xFF737373),
                          height: MediaQuery.of(context).size.height / 10,
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        NarFormLabelWidget(
                          label: 'Carte di pagamento',
                          fontWeight: '800',
                          fontSize: 13,
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label: '',
                          fontWeight: '800',
                          fontSize: 10,
                          textColor: Color(0xff878787),
                          letterSpacing: 1,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                ],
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
        Column(children: [
          NarFormLabelWidget(
            label: 'Non hai tutto il materiale a disposizione?',
            fontWeight: '800',
            fontSize: 16,
            textColor: Theme.of(context).disabledColor,
          ),
          SizedBox(
            height: 5,
          ),
          NarFormLabelWidget(
            label: 'Non preoccuparti: potrai aggiungere in seguito il materiale mancante.',
            fontWeight: '500',
            fontSize: 14,
            textColor: Theme.of(context).disabledColor,
            letterSpacing: 1,
          )
        ]),
      ],
    );
  }

  Widget localizzazioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Localizzazione',
          fontWeight: '800',
          fontSize: 23,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
            padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 10),
            child:
            Container(
                height: MediaQuery.of(context).size.height / 3,
                width: 400,
                child: Column(
                  children: [
                    AddressSearchBar(
                      label: 'Indirizzo',
                      onPlaceSelected: (selectedPlace) async {
                        if (BaseAddressInfo.fromMap(selectedPlace["place"]).isValidAddress()){
                          setState(() {
                            selectedProject!.streetName = selectedPlace["place"]["streetName"];
                            selectedProject!.streetNumber = selectedPlace["place"]["streetNumber"];
                            selectedProject!.city = selectedPlace["place"]["city"];
                            selectedProject!.postalCode = selectedPlace["place"]["postalCode"];
                            selectedProject!.province = selectedPlace["place"]["province"];
                            selectedProject!.region = selectedPlace["place"]["region"];
                            selectedProject!.country = selectedPlace["place"]["country"];
                            selectedProject!.latitude = selectedPlace["place"]["latitude"];
                            selectedProject!.longitude = selectedPlace["place"]["longitude"];
                            selectedProject!.fullAddress = selectedPlace["description"];
                            selectedProject!.addressInfo = BaseAddressInfo.fromMap(selectedPlace["place"]);
                          });
                          selectedProject!.projectId = await computeImmaginaProjectCode();
                          selectedProject!.childrenProjects!.forEach((child) {
                            child.addressInfo = BaseAddressInfo.fromMap(selectedPlace["place"]);
                          });
                        } else{
                          setState(() {
                            selectedProject!.streetName = null;
                            selectedProject!.streetNumber = null;
                            selectedProject!.city = null;
                            selectedProject!.postalCode = null;
                            selectedProject!.province = null;
                            selectedProject!.region = null;
                            selectedProject!.country = null;
                            selectedProject!.latitude = null;
                            selectedProject!.longitude = null;
                            selectedProject!.fullAddress = null;
                            selectedProject!.addressInfo = null;
                          });
                        }
                      },
                      initialAddress: selectedProject!.fullAddress,
                    ),
                    (cityMarketZoneMap.containsKey(selectedProject!.city)) ?
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 12,),
                          NarFormLabelWidget(
                            label: 'Zona',
                            textColor: Color(0xff696969),
                            fontSize: 13,
                            fontWeight: '600',
                            textAlign: TextAlign.left,
                          ),
                          SizedBox(height: 5,),
                          SearchSelectBox(
                              controller: filterMarketZone,
                              options: [
                                for (String value in cityMarketZoneMap[selectedProject!.city] ?? []) {'id': value, 'name': value.toTitleCase()}
                              ],
                              onSelection: (selected) {
                                setState(() {
                                  selectedProject!.marketZone = filterMarketZone.text;
                                });
                              }
                          )
                        ]
                    )
                        : SizedBox(),
                  ],
                )
            )
        ),
      ],
    );
  }

  Widget infoGeneraliDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Informazioni generali',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 25),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                width: 1,
                color: Color.fromARGB(255, 180, 180, 180),
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            height: MediaQuery.of(context).size.height / 2.1,
            child: ListView(
              padding: EdgeInsets.all(10),
              children: [
                ...(selectedProject!.childrenProjects?.map((child){
                  int childIndex = selectedProject!.childrenProjects!.indexOf(child);
                  Map<String, TextEditingController> childControllers = infoGeneraliTaglioControllerList[childIndex];
                  TextEditingController filterPropertyType = childControllers['propertyType']!;
                  TextEditingController filterRooms = childControllers['rooms']!;
                  TextEditingController filterBathrooms = childControllers['bathrooms']!;
                  TextEditingController filterUnitFloor = childControllers['unitFloor']!;
                  TextEditingController filterGSF = childControllers['GSF']!;
                  TextEditingController filterListingPrice = childControllers['listingPrice']!;
                  return Container(
                    width: MediaQuery.of(context).size.width / 2,
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                    margin: EdgeInsets.only(bottom: 10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        width: 1,
                        color: Color(0xffDBDBDB),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            NarFormLabelWidget(
                              label: "Taglio ${childIndex + 1}",
                              fontSize: 16,
                              fontWeight: '800',
                              textColor: Colors.black,
                            ),
                            childIndex == 0 ? SizedBox() :
                            IconButton(
                              icon: Icon(Icons.delete),
                              onPressed: () async {
                                // delete child data from firebase storage
                                Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/tagli/${child.id}');
                                await ref.delete();
                                // remove from selected project
                                setState(() {
                                  selectedProject!.childrenProjects!.removeAt(childIndex);
                                  infoGeneraliTaglioControllerList.removeAt(childIndex);
                                  caratteristicheTaglioControllerList.removeAt(childIndex);
                                  materialiTaglioControllerList.removeAt(childIndex);
                                });
                              },
                            )
                          ],
                        ),
                        SizedBox(height: 10,),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              flex: 2,
                              child: NarSelectBoxWidget(
                                label: "Tipologia",
                                options: appConst.propertyTypesList,
                                controller: filterPropertyType,
                                onChanged: ((value) {
                                  setState(() {
                                    selectedProject!.childrenProjects![childIndex].propertyType = filterPropertyType.text;
                                  });
                                }),
                              ),
                            ),
                            SizedBox(width: 5),
                            Expanded(
                              flex: 1,
                              child: NarSelectBoxWidget(
                                label: "Locali",
                                options: appConst.localiList,
                                controller: filterRooms,
                                onChanged: ((value) {
                                  setState(() {
                                    selectedProject!.childrenProjects![childIndex].rooms = int.tryParse(filterRooms.text.replaceAll(r'+', ''));
                                  });
                                }),
                              ),
                            ),
                            SizedBox(width: 5),
                            Expanded(
                              flex: 1,
                              child: NarSelectBoxWidget(
                                label: "Bagni",
                                options: appConst.bagniList,
                                controller: filterBathrooms,
                                onChanged: ((value) {
                                  setState(() {
                                    selectedProject!.childrenProjects![childIndex].numberOfBathrooms = int.tryParse(filterBathrooms.text.replaceAll(r'+', ''));
                                  });
                                }),
                              ),
                            ),
                            SizedBox(width: 5),
                            Expanded(
                              flex: 1,
                              child: NarSelectBoxWidget(
                                label: "Piano",
                                options: appConst.pianoList,
                                controller: filterUnitFloor,
                                onChanged: ((value) {
                                  setState(() {
                                    selectedProject!.childrenProjects![childIndex].unitFloor = filterUnitFloor.text;
                                  });
                                }),
                              ),
                            ),
                            SizedBox(width: 5),
                            CustomTextFormField(
                              flex: 1,
                              label: "m² commerciali",
                              controller: filterGSF,
                              suffixIcon: Padding(
                                padding: const EdgeInsets.only(right: 5.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "m²",
                                      fontSize: 13,
                                      textColor: const Color(0xff696969),
                                      fontWeight: "500",
                                      // textAlign: TextAlign.end,
                                    ),
                                  ],
                                ),
                              ),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))
                              ],
                              onChangedCallback: (value) {
                                setState(() {
                                  selectedProject!.childrenProjects![childIndex].grossSquareFootage = int.tryParse(filterGSF.text);
                                });
                              },
                            ),
                            SizedBox(width: 5),
                            CustomTextFormField(
                              label: "Richiesta",
                              flex: 2,
                              controller: filterListingPrice,
                              isIntegerMoney: true,
                              isShowPrefillMoneyIcon: true,
                              onChangedCallback: (value){
                                setState(() {
                                  selectedProject!.childrenProjects![childIndex].listingPrice = int.tryParse(value.replaceAll('.', ''));
                                });
                              },
                            ),
                          ],
                        )
                      ]
                    ),
                  );
                }).toList() ?? [])
              ]
            ),
          ),
        ),
        SizedBox(height: 15),
        BaseNewarcButton(
          buttonText: 'Aggiungi Taglio',
          width: 200,
          color: Colors.black,
          onPressed: (){
            ImmaginaProject _proj = ImmaginaProject.empty();
            _proj.agencyId = selectedProject!.agencyId;
            _proj.isForProfessionals = true;
            _proj.addressInfo = selectedProject!.addressInfo;
            _proj.id = generateRandomString(13);
            setState(() {
              selectedProject!.childrenProjects ??= [];
              selectedProject!.childrenProjects!.add(_proj);
              infoGeneraliTaglioControllerList.add({
                'propertyType': TextEditingController(),
                'rooms': TextEditingController(),
                'bathrooms': TextEditingController(),
                'unitFloor': TextEditingController(),
                'GSF': TextEditingController(),
                'listingPrice': TextEditingController(),
              });
              caratteristicheTaglioControllerList.add({
                'energyClass': TextEditingController(),
                'constructionYear': TextEditingController(),
              });
              materialiTaglioControllerList.add(TextEditingController());
            });
          }
        ),
      ],
    );
  }

  Widget descrizioneDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Descrizione',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Aggiungi una descrizione generale del progetto',
                textColor: Color(0xff696969),
                fontSize: 13,
                fontWeight: '600',
                textAlign: TextAlign.left,
              ),
              SizedBox(
                height: 5,
              ),
              Container(
                height: MediaQuery.of(context).size.height / 2,
                width: MediaQuery.of(context).size.width / 2,
                child: TextField(
                  textAlign: TextAlign.left,
                  textAlignVertical: TextAlignVertical.top,
                  minLines: null,
                  maxLines: null,
                  expands: true,
                  keyboardType: TextInputType.multiline,
                  controller: filterDescription,
                  onChanged: (desc) {
                    setState(() {
                      selectedProject!.description = filterDescription.text;
                    });
                  },
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                    fontFamily: 'Raleway-600',
                  ),
                  decoration: InputDecoration(
                      hintText: 'Scrivi qui una descrizione generale per descrivere il progetto.',
                      hintStyle: TextStyle(
                        color: Color.fromARGB(255, 150, 150, 150),
                        fontSize: 15,
                        fontFamily: 'Raleway-600',
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(color: Color(0xffdbdbdb)),
                      )),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  /* Widget externalEspositionButton(String label, ImmaginaProject project){
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          side: BorderSide(
              color: project.externalEsposition.contains(label)? Colors.transparent : Color(0xffdbdbdb)
          ),
          backgroundColor: project.externalEsposition.contains(label)? Theme.of(context).primaryColor : Colors.transparent,
        ),
        onPressed: () {
          if (project.externalEsposition.contains(label)) {
            setState(() {
              project.externalEsposition.removeWhere((item) => item == label);
            });
          } else {
            setState(() {
              project.externalEsposition.add(label);
            });
          }
        },
        child: Text(
          label,
          style: TextStyle(
            color: project.externalEsposition.contains(label) ? Theme.of(context).unselectedWidgetColor : Theme.of(context).primaryColorDark,
            fontSize: 14,
            fontFamily: 'Raleway-600',
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  } */

  Widget caratteristicheDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Caratteristiche',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 25),
          child: 
          Container(
            height: MediaQuery.of(context).size.height / 1.8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Color.fromARGB(255, 180, 180, 180),
                width: 1,
              ),
            ),
            child: ListView(
              padding: EdgeInsets.all(10),
              children: [
                ...(selectedProject!.childrenProjects?.map((child){
                  int childIndex = selectedProject!.childrenProjects!.indexOf(child);
                  String locali = localiMap[child.rooms ?? 1] ?? '';
                  Map<String, dynamic> characteristics = Map.from(appConst.houseFeatures);
                  characteristics['Ascensore'] = child.elevator ?? false;
                  characteristics['Cantina'] = child.hasCantina ?? false;
                  characteristics['Terrazzo'] = child.terrace ?? false;
                  characteristics['Portineria'] = child.hasConcierge ?? false;
                  characteristics['Infissi ad alta efficienza'] = child.highEfficiencyFrames ?? false;
                  characteristics['Doppia esposizione'] = child.doubleEsposition ?? false;
                  characteristics['Tripla esposizione'] = child.tripleEsposition ?? false;
                  characteristics['Quadrupla esposizione'] = child.quadrupleEsposition ?? false;
                  characteristics['Risc. centralizzato'] = child.centralizedHeating ?? false;
                  characteristics['Risc. autonomo'] = child.autonomousHeating ?? false;
                  characteristics['Giardino privato'] = child.privateGarden ?? false;
                  characteristics['Giardino condominiale'] = child.sharedGarden ?? false;
                  characteristics['Stabile signorile'] = child.nobleBuilding ?? false;
                  characteristics['Stabile videosorvegliato'] = child.surveiledBuilding ?? false;
                  characteristics['Fibra ottica'] = child.fiber ?? false;
                  characteristics['Pred. condizionatore'] = child.airConditioning ?? false;
                  characteristics['Porta blindata'] = child.securityDoor ?? false;
                  characteristics['Impianto TV'] = child.tvStation ?? false;
                  characteristics['Pred. antifurto'] = child.alarm ?? false;
                  characteristics['Tapparelle motorizzate'] = child.motorizedSunblind ?? false;
                  characteristics['Tapparelle domotizzate'] = child.domotizedSunblind ?? false;
                  characteristics['Luci domotizzate'] = child.domotizedLights ?? false;
                  characteristics['Piano alto'] = child.highFloor ?? false;
                  characteristics['Vicinanza Metro'] = child.metroVicinity ?? false;
                  characteristics['Ampi balconi'] = child.bigBalconies ?? false;
                  characteristics['Grande zona living'] = child.bigLiving ?? false;
                  characteristics['Doppi servizi'] = child.doubleBathroom ?? false;
                  characteristics['Piscina'] = child.swimmingPool ?? false;
                  characteristics['Box o garage'] = child.hasGarage ?? false;
                  characteristics['Cabina armadio'] = child.walkInCloset ?? false;
                  characteristics['Fotovoltaico'] = child.solarPanel ?? false;
                  
                  TextEditingController filterEnergyClass = caratteristicheTaglioControllerList[childIndex]['energyClass']!;
                  TextEditingController filterConstructionYear = caratteristicheTaglioControllerList[childIndex]['constructionYear']!;

                  return Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                    margin: EdgeInsets.only(bottom: 10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        width: 1,
                        color: Color(0xffDBDBDB),
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        NarFormLabelWidget(
                          label: 'Taglio ${childIndex + 1} - ${locali} - ${child.grossSquareFootage ?? "N.A."} mq',
                          fontSize: 16,
                          fontWeight: '800',
                          textColor: Colors.black,
                        ),
                        SizedBox(height: 20,),
                        NarCheckboxWidget(
                          label: 'characteristics',
                          values: characteristics,
                          columns: 4,
                          fontSize: 13,
                          childAspectRatio: 5,
                          onChanged: (value) {
                            setState(() {
                              saveCharacteristics(child, characteristics);
                            });
                          },
                        ),
                        SizedBox(height: 20,),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                              child: NarSelectBoxWidget(
                                label: 'Classe energetica',
                                options: appConst.energyClassList,
                                controller: filterEnergyClass,
                                onChanged: ((value) {
                                  setState(() {
                                    child.energyClass = filterEnergyClass.text;
                                  });
                                }),
                              ),
                            ),
                            SizedBox(width: 30,),
                            CustomTextFormField(
                              label: "Anno di costruzione",
                              controller: filterConstructionYear,
                              onChangedCallback: (value) {
                                String constYear = filterConstructionYear.text;
                                if (constYear == null || constYear.isEmpty || int.tryParse(constYear) == null) {
                                  setState(() {
                                    child.constructionYear = null;
                                  });
                                } else {
                                  setState(() {
                                    child.constructionYear = int.tryParse(filterConstructionYear.text);
                                  });
                                };
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty || int.tryParse(value) == null) {
                                  return 'Inserisci un anno di costruzione valido';
                                }
                                return null;
                              },
                            ),
                            SizedBox(width: 30,),
                            Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarFormLabelWidget(
                                    label: 'Esposizione',
                                    textColor: Color(0xff696969),
                                    fontSize: 13,
                                    fontWeight: '500',
                                  ),
                                  SizedBox(height: 15,),
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      externalEspositionButton('Nord', child, context, stateSetter: setState),
                                      externalEspositionButton('Sud', child, context, stateSetter: setState),
                                      externalEspositionButton('Ovest', child, context, stateSetter: setState),
                                      externalEspositionButton('Est', child, context, stateSetter: setState),
                                    ],
                                  )
                                ]
                            )
                          ],
                        ),
                        ...childIndex == 0 ? [
                          SizedBox(height: 20,),
                          Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: OutlinedButton(
                              style: OutlinedButton.styleFrom(
                                backgroundColor: Color.fromARGB(255, 219, 219, 219),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                side: BorderSide(
                                    color: Colors.transparent
                                ),
                              ),
                              onPressed: () {
                                selectedProject!.childrenProjects!.forEach((son) {
                                  int index = selectedProject!.childrenProjects!.indexOf(son);
                                  setState(() {
                                    son.elevator = selectedProject!.childrenProjects![0].elevator;
                                    son.hasCantina = selectedProject!.childrenProjects![0].hasCantina;
                                    son.terrace = selectedProject!.childrenProjects![0].terrace;
                                    son.hasConcierge = selectedProject!.childrenProjects![0].hasConcierge;
                                    son.highEfficiencyFrames = selectedProject!.childrenProjects![0].highEfficiencyFrames;
                                    son.doubleEsposition = selectedProject!.childrenProjects![0].doubleEsposition;
                                    son.tripleEsposition = selectedProject!.childrenProjects![0].tripleEsposition;
                                    son.quadrupleEsposition = selectedProject!.childrenProjects![0].quadrupleEsposition;
                                    son.centralizedHeating = selectedProject!.childrenProjects![0].centralizedHeating;
                                    son.autonomousHeating = selectedProject!.childrenProjects![0].autonomousHeating;
                                    son.privateGarden = selectedProject!.childrenProjects![0].privateGarden;
                                    son.sharedGarden = selectedProject!.childrenProjects![0].sharedGarden;
                                    son.nobleBuilding = selectedProject!.childrenProjects![0].nobleBuilding;
                                    son.surveiledBuilding = selectedProject!.childrenProjects![0].surveiledBuilding;
                                    son.fiber = selectedProject!.childrenProjects![0].fiber;
                                    son.airConditioning = selectedProject!.childrenProjects![0].airConditioning;
                                    son.securityDoor = selectedProject!.childrenProjects![0].securityDoor;
                                    son.tvStation = selectedProject!.childrenProjects![0].tvStation;
                                    son.alarm = selectedProject!.childrenProjects![0].alarm;
                                    son.motorizedSunblind = selectedProject!.childrenProjects![0].motorizedSunblind;
                                    son.domotizedSunblind = selectedProject!.childrenProjects![0].domotizedSunblind;
                                    son.domotizedLights = selectedProject!.childrenProjects![0].domotizedLights;
                                    son.highFloor = selectedProject!.childrenProjects![0].highFloor;
                                    son.metroVicinity = selectedProject!.childrenProjects![0].metroVicinity;
                                    son.bigBalconies = selectedProject!.childrenProjects![0].bigBalconies;
                                    son.bigLiving = selectedProject!.childrenProjects![0].bigLiving;
                                    son.doubleBathroom = selectedProject!.childrenProjects![0].doubleBathroom;
                                    son.swimmingPool = selectedProject!.childrenProjects![0].swimmingPool;
                                    son.hasGarage = selectedProject!.childrenProjects![0].hasGarage;
                                    son.solarPanel = selectedProject!.childrenProjects![0].solarPanel;
                                    son.walkInCloset = selectedProject!.childrenProjects![0].walkInCloset;
                                    son.energyClass = selectedProject!.childrenProjects![0].energyClass;
                                    son.constructionYear = selectedProject!.childrenProjects![0].constructionYear;
                                    son.externalEsposition = selectedProject!.childrenProjects![0].externalEsposition.map((label) => label).toList();
                                    caratteristicheTaglioControllerList[index]['energyClass']!.text = son.energyClass ?? "";
                                    caratteristicheTaglioControllerList[index]['constructionYear']!.text = selectedProject!.childrenProjects![0].constructionYear != null ? selectedProject!.childrenProjects![0].constructionYear.toString() : "";
                                  });
                                });
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  Icon(
                                    Icons.arrow_downward_rounded,
                                    color: Colors.black,
                                    size: 20,
                                  ),
                                  Text(
                                    "Applica a tutti i tagli",
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                      fontFamily: "Raleway-600",
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        ] : []
                      ],
                    ),
                  );
                }).toList() ?? [])
              ]
            )
          )
        )
      ],
    );
  }

  getPlanimetryFromGallery(
      BuildContext context,
      List<XFile> planimetryImages,
      ) async {
    bool wrongExtension = false;
    bool wrongSize = false;
    await FilePicker.platform
        .pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: allowedPlanimetryExtensions,
    )
        .then((filesList) {
      if (filesList != null) {
        filesList.files.forEach((file) {
          if (file.size < 10 * 1024 * 1024) {
            if (allowedPlanimetryExtensions.contains(file.extension)) {
              planimetryImages.add(file.xFile);
            } else {
              wrongExtension = true;
            }
          } else {
            wrongSize = true;
          }
        });
      }
      setState(() {});
      if (wrongExtension) {
        return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Estensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di planimetrie con estensione: ${allowedPlanimetryExtensions.join(', ')}'),
                  ],
                ),
              ),
            );
          },
        );
      }
      if (wrongSize) {
        return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Errore: Dimensione file non valida',
                column: Column(
                  children: [
                    Text('Inserisci file di planimetrie con dimensione massima 10 MB'),
                  ],
                ),
              ),
            );
          },
        );
      }
    });
  }

  _showUploadPlanimetryDialog(BuildContext context, ImmaginaProject childProject,List<XFile> planimetryImages) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Carica immagini',
              noButton: true,
              column: StatefulBuilder(builder: (BuildContext context, StateSetter _setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: GestureDetector(
                            onTap: () async {
                              await getPlanimetryFromGallery(context, planimetryImages);
                              await savePlanimetryImages(childProject, planimetryImages);
                              _setState(() {});
                              setState(() {});
                              Navigator.of(context).pop(true);
                            },
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Color.fromRGBO(240, 240, 240, 1),
                                    //shape: BoxShape.circle,
                                    borderRadius: BorderRadius.all(Radius.circular(10))),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 60,
                                      color: Color.fromRGBO(128, 128, 128, 1),
                                    ),
                                    SizedBox(
                                      height: 30,
                                    ),
                                    NarFormLabelWidget(
                                      label: "Clicca per caricare",
                                      fontWeight: '700',
                                      fontSize: 18,
                                      textColor: Color.fromRGBO(128, 128, 128, 1),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  Widget planimetriaDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Planimetrie di progetto',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 60),
          child: Container(
            child: Column(
              children: [
                SizedBox(
                  height: 10,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.info,
                      size: 20,
                      color: Color(0xffbdbdbd),
                    ),
                    Expanded(      
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'La planimetria deve essere ben visibile e comprensibile in tutte le sue parti (con riferimento di scala grafica) e deve essere corrispondente allo stato reale, \nidealmente in fromato DWG, altrimenti JPEG o PDF. Se possibile, carica una planimetria già arredata',
                              style: TextStyle(
                                fontFamily: 'Raleway-600',
                                fontSize: 13,
                                color: Color.fromRGBO(133, 133, 133, 1),
                                height: 1.5,
                              ),
                            ),
                          ]
                        )
                      )
                    ),
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                // Image picker
                Container(
                  height: (MediaQuery.of(context).size.height / 2.2),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: Color(0xffd3d3d3),
                    ),
                  ),
                  child: ListView(
                    padding: EdgeInsets.all(10),
                    children: [
                      ...(selectedProject!.childrenProjects!.map((child){
                        int childIndex = selectedProject!.childrenProjects!.indexOf(child);
                        String locali = localiMap[child.rooms ?? 1] ?? '';
                        return Container(
                          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                          margin: EdgeInsets.only(bottom: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: Color(0xffDBDBDB),
                              width: 1,
                            ),
                          ),
                          child: FutureBuilder(
                            future: getFirestoreImages(child),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState == ConnectionState.waiting) {
                                return Center(child: CircularProgressIndicator());
                              }
                              else if (snapshot.hasError) {
                                return Center(child: Text('Error: ${snapshot.error}'));
                              }
                              else if (snapshot.hasData) {
                                List<XFile> planimetryImages = snapshot.data as List<XFile>;
                                return Stack(
                                children: [
                                  NarFormLabelWidget(
                                    label: 'Taglio ${childIndex + 1} - ${locali} - ${child.grossSquareFootage ?? "N.A."} mq',
                                    textAlign: TextAlign.center,
                                    fontWeight: '800',
                                    fontSize: 13,
                                    textColor: Colors.black,
                                  ),
                                  SingleChildScrollView(
                                    padding: EdgeInsets.only(top: 20, bottom: 20, left: 8, right: 100),
                                    child: Wrap(
                                      spacing: 12,
                                      runSpacing: 20,
                                      children: planimetryImages
                                        .map((imageFile) => FutureBuilder<Uint8List>(
                                          future: imageFile.readAsBytes(),
                                          builder: (context, snapshot) {
                                            if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                                              return Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Stack(
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius: BorderRadius.circular(8),
                                                        child: imageFile.name.split('.').last.toLowerCase() == 'dwg'
                                                            ? Image.asset(
                                                          'assets/icons/dwg.png',
                                                          color: Color(0xffa6a6a6),
                                                          width: 150,
                                                          height: 150,
                                                          fit: BoxFit.cover,
                                                        )
                                                            : imageFile.name.split('.').last.toLowerCase() == 'pdf'
                                                            ? Image.asset(
                                                          'assets/icons/pdf.png',
                                                          color: Color(0xffa6a6a6),
                                                          width: 150,
                                                          height: 150,
                                                          fit: BoxFit.cover,
                                                        )
                                                            : Image.memory(
                                                          snapshot.data!,
                                                          width: 150,
                                                          height: 150,
                                                          fit: BoxFit.cover,
                                                        ),
                                                      ),
                                                      Positioned(
                                                        top: 3,
                                                        right: 3,
                                                        child: MouseRegion(
                                                          cursor: SystemMouseCursors.click,
                                                          child: GestureDetector(
                                                            onTap: () async {
                                                              // remove image from child.planimetry and from storage
                                                              selectedProject!.childrenProjects![childIndex].planimetry.remove(imageFile.name);
                                                              await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
                                                              Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/tagli/${child.id}/${imageFile.name}');
                                                              await ref.delete();
                                                              setState(() {});
                                                            },
                                                            child: Container(
                                                              decoration: BoxDecoration(
                                                                shape: BoxShape.circle,
                                                                color: Theme.of(context).primaryColorDark,
                                                              ),
                                                              child: Padding(
                                                                padding: EdgeInsets.all(3),
                                                                child: SvgPicture.asset(
                                                                  'assets/icons/close-popup.svg',
                                                                  height: 10,
                                                                  color: Theme.of(context).unselectedWidgetColor,
                                                                )
                                                              )
                                                            )
                                                          ),
                                                        )
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              );
                                            } else if (snapshot.connectionState == ConnectionState.waiting) {
                                              return Container(
                                                width: 80,
                                                height: 80,
                                                color: Colors.grey[300],
                                                child: Center(
                                                  child: CircularProgressIndicator(),
                                                ),
                                              );
                                            } else {
                                              return Container(
                                                width: 80,
                                                height: 80,
                                                color: Colors.grey[300],
                                                child: Center(
                                                  child: Icon(Icons.error, color: Colors.red),
                                                ),
                                              );
                                            }
                                          },
                                        )
                                      ).toList(),
                                    ),
                                  ),
                                  Positioned(
                                    top: 0,
                                    right: 8,
                                    child: BaseNewarcButton(
                                      color: Color(0xffE5E5E5),
                                      textColor: Colors.black,
                                      buttonText: "Carica",
                                      fontWeight: '700',
                                      fontSize: 14,
                                      height: 35,
                                      onPressed: () async {
                                        await _showUploadPlanimetryDialog(context, child, planimetryImages);
                                      },
                                    ),
                                  ),
                                ],
                              );
                              } else {
                                return Center(child: Text('No Data'));
                              }
                            }
                          ),
                        );
                      }))
                    ],
                  ),
                ), 
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget materialiDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NarFormLabelWidget(
          label: 'Indicazioni sui materiali',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(
          height: 10,
        ),
        SizedBox(
          height: 50,
          width: 400,
          child: BarToggleButton(
            key: UniqueKey(),
            startingState: (selectedProject!.wantsMaterialNotes),
            onStateChanged: (selection) {
              setState(() {
                selectedProject!.wantsMaterialNotes = selection;
              });
            },
            trueStateText: 'Fornisci indicazioni',
            falseStateText: 'Non fornire indicazioni',
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 10),
          child: Container(
            height: MediaQuery.of(context).size.height / 1.9,
            child: Column(
              children: [
                ...(selectedProject!.wantsMaterialNotes)
                  ? [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.info,
                          size: 20,
                          color: Color(0xffbdbdbd),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Text("Per ogni taglio, forniscici delle indicazioni sui materiali che applicherai nella ristrutturazione. \nRealizzeremo i render cercando di essere il più fedeli possibile ai materiali da te indicati.",
                          style: TextStyle(
                            fontFamily: 'Raleway-600',
                            fontSize: 13,
                            color: Color.fromRGBO(133, 133, 133, 1),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    // Materials notes picker
                    Container(
                      height: MediaQuery.of(context).size.height / 2.25,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: Color(0xffd3d3d3),
                        ),
                      ),
                      child: ListView(
                        padding: EdgeInsets.all(10),
                        children: [
                          ...(selectedProject!.childrenProjects!.map((child){
                            int childIndex = selectedProject!.childrenProjects!.indexOf(child);
                            String locali = localiMap[child.rooms ?? 1] ?? '';
                            TextEditingController _materialsNotesController = materialiTaglioControllerList[childIndex];
                            return Container(
                              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                              margin: EdgeInsets.only(bottom: 10),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: Color(0xffDBDBDB),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarFormLabelWidget(
                                    label: 'Taglio ${childIndex + 1} - ${locali} - ${child.grossSquareFootage ?? "N.A."} mq',
                                    fontSize: 16,
                                    fontWeight: '800',
                                    textColor: Colors.black,
                                  ),
                                  SizedBox(height: 10,),
                                  TextField(
                                    controller: _materialsNotesController,
                                    maxLines: 3,
                                    decoration: InputDecoration(
                                      hintText: 'Inserisci qui le indicazioni sui materiali utilizzati in questo specifico taglio',
                                      hintStyle: TextStyle(
                                        fontFamily: 'Raleway-600',
                                        fontSize: 13,
                                        color: Color.fromRGBO(133, 133, 133, 1),
                                      ),
                                      border: InputBorder.none,
                                      enabledBorder: InputBorder.none,
                                      focusedBorder: InputBorder.none,
                                      contentPadding: EdgeInsets.zero,
                                    ),
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 15,
                                      fontFamily: 'Raleway-600',
                                    ),
                                    onChanged: (some) {
                                      setState(() {
                                        selectedProject!.childrenProjects![childIndex].materialsNotes = _materialsNotesController.text;
                                      });
                                    },
                                  ),
                                ],
                              ),
                            );
                          }).toList())
                        ]
                      ),
                    ),
                  ]
                  : [
                    Expanded(
                      child: Container(
                        width: MediaQuery.of(context).size.width / 2,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SizedBox(
                              height: 20,
                            ),
                            SvgPicture.asset(
                              'assets/icons/arrow.svg',
                              width: 30,
                              // color: Color(0xffa6a6a6),
                              color: Colors.black,
                            ),
                            Text(
                              'Hai scelto di non fornire indicazioni sui materiali, \nclicca su avanti per continuare.',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Theme.of(context).disabledColor,
                                fontSize: 20,
                                fontFamily: 'Raleway-700',
                              ),
                            ),
                            SizedBox(
                              height: 20,
                            ),
                          ],
                        )
                      )
                    )
                  ]
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget indicazioniSpecialiDialog() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Altre indicazioni',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(height: 30),
        Container(
          height: MediaQuery.of(context).size.height / 1.7,
          width: MediaQuery.of(context).size.width / 2.5,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Hai indicazioni speciali da darci riguardo al progetto?",
                style: TextStyle(
                  fontFamily: 'Raleway-700',
                  fontSize: 15,
                  color: Theme.of(context).disabledColor,
                ),
                textAlign: TextAlign.left,
              ),
              SizedBox(height: 5),
              Text("Es: la colonna della sala è portante",
                  style: TextStyle(
                    fontFamily: 'Raleway-500',
                    fontSize: 13,
                    color: Theme.of(context).primaryColorLight,
                  )),
              SizedBox(height: 15),
              SiNoToggleButton(
                startingState: selectedProject!.hasSpecialHints ?? false,
                onStateChanged: (value) {
                  setState(() {
                    selectedProject!.hasSpecialHints = value;
                  });
                },
              ),
              SizedBox(height: 30),
              Container(
                child: CustomTextFormField(
                  isExpanded: false,
                  label: 'Scrivici qui le tue indicazioni speciali',
                  controller: filterSpecialHints,
                  onChangedCallback: (value) {
                    setState(() {
                      selectedProject!.specialHints = value;
                    });
                  },
                  enabled: selectedProject!.hasSpecialHints ?? false,
                  fillColor: (selectedProject!.hasSpecialHints ?? false) ? Theme.of(context).unselectedWidgetColor : Colors.grey[100],
                  minLines: 6,
                ),
              ),
              SizedBox(height: 30),
              NarFormLabelWidget(
                label: "Quando vorresti mettere in vendita l’immobile?",
                fontSize: 15,
                fontWeight: '700',
                textColor: Theme.of(context).disabledColor,
                textAlign: TextAlign.left,
              ),
              SizedBox(height: 10),
              SizedBox(
                height: 75,
                child: NarSelectBoxWidget(
                  label: "Seleziona una risposta",
                  options: [
                    "L'immobile è gia in vendita",
                    "Appena pronto il progetto Immagina",
                    "Entro un mese",
                    "Entro tre mesi",
                  ],
                  onChanged: (value) {
                    if(value != null && value.toString().isNotEmpty){
                      setState(() {
                        selectedProject!.propertyUpForSaleAnswer = value;
                      });
                    }
                  },
                  controller: filterPropertyUpForSaleAnswer,
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget optionalsDialog() {
    ImmaginaProjectEconomics economics = ImmaginaProjectEconomics(project: selectedProject!);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Aggiungi optional',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        SizedBox(height: 50),
        Container(
          height: MediaQuery.of(context).size.height / 1.8,
          child: GridView.builder(
            padding: EdgeInsets.only(bottom: 10),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 2,
              crossAxisSpacing: 40,
              mainAxisSpacing: 20,
            ),
            itemCount: economics.professionalsOptionals.keys.length,
            itemBuilder: (context, index) {
              String optionalName = economics.professionalsOptionals.keys.toList()[index];
              String optionalDescription = economics.professionalsOptionals[optionalName]!['description'] ?? '';
              int optionalPrice = economics.professionalsOptionals[optionalName]!['price'] ?? 0;
              bool isSelected = selectedProject!.optionals.contains(optionalName);
              return MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        selectedProject!.optionals.remove(optionalName);
                      } else {
                        selectedProject!.optionals.add(optionalName);
                      }
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      border: isSelected
                        ? Border.all(
                            color: Colors.black,
                            width: 2,
                          )
                        : Border.all(
                            color: Color(0xffDBDBDB),
                            width: 1,
                          ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          optionalName,
                          style: TextStyle(
                            fontFamily: 'Raleway-800',
                            fontSize: 16,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                          // textAlign: TextAlign.left,
                        ),
                        SizedBox(height: 5,),
                        Text(
                          optionalDescription,
                          style: TextStyle(
                            fontFamily: 'Raleway-500',
                            fontSize: 14,
                            color: Theme.of(context).disabledColor,
                          ),
                          overflow: TextOverflow.clip,
                          // textAlign: TextAlign.left,
                        ),
                        Expanded(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                '${optionalPrice}€',
                                style: TextStyle(
                                  fontFamily: 'Raleway-700',
                                  fontSize: 16,
                                  color: Theme.of(context).disabledColor,
                                ),
                                overflow: TextOverflow.fade,
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
          ),
        )
      ],
    );
  }

  Widget pagamentoDialog() {
    
    // whether at least one taglio exceeds gSF limit
    final bool gSFExceeded = (selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty)
        ? selectedProject!.childrenProjects!.any((child) => child.grossSquareFootage! >= professionalsGSFUpperLimit)
        : false;
    
    // if gSFExceeded change project status -> preventivazione and send emails
    if (gSFExceeded && selectedProject!.requestStatus == 'da completare') {
      try {
        final int time_now = DateTime.now().millisecondsSinceEpoch;
        Future.delayed(Duration.zero, () async {
          // update project status on firebase
          final projectDocRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id);
          projectDocRef.update({
            'requestStatus': 'preventivazione',
            'isPaidWithCredits': false,
            'appliedSuccessFee': "0",
            'statusChangedDate': time_now,
          });
          // send email to agency
          sendEmail(
            templateId: CommonUtils.agencyNewOversizedRequestEmailTemplateId, 
            agency: widget.agencyUser.agency!, 
            subject: CommonUtils.agencyNewOversizedRequestEmailSubject, 
          );
          // send email to ourselves 
          Map<String,dynamic> emailDataForUs = {
            "agencyname": widget.agencyUser.agency?.name,
            "immaginaprojectid": selectedProject!.projectId,
          };
          final renderistUsers = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_USERS)
            .where('type', isEqualTo: 'newarc')
            .where('role', isEqualTo: 'master-renderist')
            .where('isActive', isEqualTo: true)
            .get();
          final masterUsers = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_USERS)
            .where('type', isEqualTo: 'newarc')
            .where('role', isEqualTo: 'master')
            .where('isActive', isEqualTo: true)
            .get();
          for (var userDoc in renderistUsers.docs + masterUsers.docs) {
            final userData = userDoc.data();
            if (userData['email'] != null) {
              sendEmail(
                templateId: CommonUtils.agencyNewOversizedRequestForWorkSideEmailTemplateId, 
                agency: Agency.empty(), 
                subject: CommonUtils.agencyNewOversizedRequestForWorkSideEmailSubject, 
                variables: emailDataForUs, 
                recipientEmail: userData['email'], 
                recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
              );
            }
          }
        });
        // update project status on client
        setState(() {
          selectedProject!.requestStatus = 'preventivazione';
          selectedProject!.isPaidWithCredits = false;
          selectedProject!.appliedSuccessFee = '0';
          selectedProject!.statusChangedDate = time_now;
        });
      } catch (e) {
        print("Error in pagamentoDialog while updating document and sending emails: $e");
      }
    }
    /*
    final int subscriptionServiceCountLeft = widget.agencyUser.agency?.subscriptionServiceCountLeft ?? 0;
    DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(widget.agencyUser.agency?.subscriptionEndDate ?? 0);
    DateTime now = DateTime.now();
    final bool isSubscriptionOver = (subscriptionServiceCountLeft <= 0 || now.isAfter(expiryDate));
    // Scenario #1: Agency has credits and uploaded required files
    final bool canCompleteUsingCredits = !isSubscriptionOver && (selectedProject?.wantsNewarcPlanimetry == false && selectedProject?.wantsNewarcPictures == false);
    // Scenario #2: Agency has credits but hasn't uploaded required files
    final bool needsToPay = !isSubscriptionOver && (selectedProject?.wantsNewarcPlanimetry == true || selectedProject?.wantsNewarcPictures == true);
    // Scenario #3: Agency has no credits or subscription expired
    print("isSubscriptionOver   ===> $isSubscriptionOver");
    print("canCompleteUsingCredits   ===> $canCompleteUsingCredits");
    print("needsToPay   ===> $needsToPay");
    */
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Pagamento',
          fontWeight: '800',
          fontSize: 22,
          textColor: Theme.of(context).disabledColor,
        ),
        Padding(
          padding: EdgeInsets.only(
            top: MediaQuery.of(context).size.height / 30,
          ),
          child: Container(
            height: MediaQuery.of(context).size.height / 1.8,
            width: MediaQuery.of(context).size.width / 2,
            child: gSFExceeded
              ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width / 3,
                    child: Text(
                      "Date le caratteristiche speciali del tuo immobile procederemo a contattarti con un preventivo personalizzato. \nRiceverai al più presto un'email al tuo indirizzo ${widget.agencyUser.email}.",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontFamily: 'Raleway-600', fontSize: 15, color: const Color.fromARGB(255, 100, 100, 100)),
                    ),
                  ),
                ],
              )
              : Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                      ),
                      // child: !isSubscriptionOver ? _payBasedOnSelection() : _fullPay(),
                      child: SingleChildScrollView(child: _fullPay()),
                    ),
                  ),
                ),
                VerticalDivider(
                  thickness: .25,
                  color: Colors.grey[500],
                  indent: 30,
                  endIndent: 30,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: loading
                        ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(color: Theme.of(context).primaryColor),
                            SizedBox(
                              height: 20,
                            ),
                            Text(
                              "In attesa del pagamento, reindirizzamento al link per il pagamento.",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontFamily: 'Raleway-600', fontSize: 15, color: Colors.grey[500]),
                            ),
                          ],
                        ))
                        : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        if (paymentError) ...[
                          Text(
                            "Qualcosa è andato storto con il pagamento, riprova.",
                            textAlign: TextAlign.center,
                            style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Colors.black),
                          ),
                          SizedBox(
                            height: 10,
                          )
                        ],
                        Column(
                          children: [
                            BaseNewarcButton(
                              buttonText: selectedProject!.receivedPayment ? 'Pagamento già effettuato!' : 'Procedi con il pagamento',
                              disableButton: selectedProject!.receivedPayment ? true : false,
                              color: selectedProject!.receivedPayment ? Color(0xff489B79) : Theme.of(context).primaryColor,
                              onPressed: selectedProject!.receivedPayment
                              ? () {}
                              : () async {
                                  setState(() {
                                  loading = true;
                                  _isFrozen = true;
                                });

                                // open tab before async calls so to avoid iOS popup blocking
                                final newTab = html.window.open('', '_blank');
                                try {
                                  // Check current payment status
                                  // avoids double payment
                                  final docRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id);
                                  var element = await docRef.get();
                                  ImmaginaProject _tmp = ImmaginaProject.fromDocument(element.data()!, element.id);
                                  if (_tmp.receivedPayment == true) {
                                    Navigator.of(context).pop();
                                    confirmationPopup(context);
                                    setState(() {
                                      loading = false;
                                      selectedProject = _tmp;
                                      paymentError = false;
                                    });
                                    return;
                                  }
                                  bool wasPaymentDetected = false;

                                  // Get payment link, redirect and set up freeze screen listener
                                  List<String> stripePriceIds = ImmaginaProjectEconomics(project: selectedProject!).getStripePriceIdsForProfessional();
                                  Map linkMap = await getStripeCheckoutLink(stripePriceIds, selectedProject!.id, widget.agencyUser.agencyId!, origin: "professionals");

                                  if (linkMap['link'] == null) {
                                    setState(() {
                                      loading = false;
                                      paymentError = true;
                                      _isFrozen = false;
                                    });
                                    newTab.close();
                                    return;
                                  }

                                  // Assign the Stripe payment link to the tab
                                  newTab.location.href = linkMap['link']!;

                                  // Check if the user closed the tab without completing payment
                                  Future.doWhile(() async {
                                    await Future.delayed(Duration(seconds: 1));
                                    return !wasPaymentDetected && newTab.closed == false;
                                  }).then((_) {
                                    if (!wasPaymentDetected) {
                                      setState(() {
                                        _isFrozen = false;
                                        loading = false;
                                        paymentError = true;
                                      });
                                    }
                                  });
                                  // Received payment listener
                                  StreamSubscription? subscription;
                                  subscription = docRef.snapshots().listen(
                                    (event) async {
                                      print('listening event');
                                      var temp = event.data();
                                      if (temp != null) {
                                        if (temp['receivedPayment'] != null) {
                                          if (temp['receivedPayment'] == true) {
                                            wasPaymentDetected = true;
                                            setState(() {
                                              loading = false;
                                              _isFrozen = false;
                                            });
                                            Navigator.of(context).pop();
                                            confirmationPopup(context);
                                            await updateAgencyAndProject(
                                              agencyId: widget.agencyUser.agencyId,
                                              projectId: selectedProject?.id,
                                              immaginaProjectId: selectedProject?.projectId,
                                              isPaidWithCredits: false,
                                            );
                                            subscription?.cancel();
                                          }
                                        }
                                      }
                                    },
                                    onError: (error) {
                                      print("Listen failed: $error");
                                      setState(() {
                                        _isFrozen = false;
                                        loading = false;
                                        paymentError = true;
                                      });
                                      subscription?.cancel();
                                    },
                                    onDone: () async {
                                      print("Listen done");
                                      await docRef.get().then((DocumentSnapshot doc) {
                                        var temp = doc.data();
                                        print("temp: $temp");
                                      });
                                    },
                                  );
                                } catch (e) {
                                  print('Error in payment button $e');
                                  setState(() {
                                    _isFrozen = false;
                                    loading = false;
                                    paymentError = true;
                                  });
                                  newTab.close();
                                }
                              }
                            ),
                            // if (canCompleteUsingCredits)
                            //   Padding(
                            //     padding: const EdgeInsets.only(top: 20.0),
                            //     child: BaseNewarcButton(
                            //       buttonText: 'Clicca qui per inviare la richiesta',
                            //       height: 50,
                            //       disableButton: false,
                            //       color: Theme.of(context).primaryColor,
                            //       onPressed: () async {
                            //         await updateAgencyAndProject(
                            //           agencyId: widget.agencyUser.agencyId,
                            //           projectId: selectedProject?.id,
                            //           isPaidWithCredits: true,
                            //           immaginaProjectId: selectedProject?.projectId,
                            //         );
                            //         Navigator.pop(context);
                            //         confirmationPopup(context);
                            //       },
                            //     ),
                            //   ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Text(
                          "Verrà aperta una nuova finestra per il pagamento tramite Stripe.",
                          textAlign: TextAlign.center,
                          style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Color(0xff4A4A4A)),
                        ),
                      ],
                    ),
                  )
                ),
              ],
            ),
          )
        )
      ],
    );
  }

  // Column _payBasedOnSelection() {
  //   return Column(
  //     mainAxisAlignment: MainAxisAlignment.start,
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Text(
  //         'Completa il servizio',
  //         style: TextStyle(fontFamily: 'Raleway-800', fontSize: 18, color: Theme.of(context).disabledColor),
  //       ),
  //       SizedBox(height: 20),
  //       Text(
  //         'Locali: ${selectedProject!.rooms}',
  //         style: TextStyle(fontFamily: 'Raleway-500', fontSize: 12, color: Colors.grey[500]),
  //       ),
  //       Padding(
  //         padding: EdgeInsets.only(top: 10),
  //         child: Row(
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             Text(
  //               'Progetto immagina:',
  //               style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //             ),
  //             Text(
  //               '1 credito',
  //               style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //             ),
  //           ],
  //         ),
  //       ),
  //       if (selectedProject?.wantsNewarcPlanimetry == true)
  //         Padding(
  //           padding: EdgeInsets.only(top: 10),
  //           child: Row(
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             children: [
  //               Text(
  //                 'Rilievi e planimetria:',
  //                 style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //               ),
  //               Text(
  //                 '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcRilieviFee()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
  //                 style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //               ),
  //             ],
  //           ),
  //         ),
  //       if (selectedProject?.wantsNewarcPictures == true)
  //         Padding(
  //           padding: EdgeInsets.only(top: 10),
  //           child: Row(
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             children: [
  //               Text(
  //                 'Fotografie e virtual tour:',
  //                 style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //               ),
  //               Text(
  //                 '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaFee()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
  //                 style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //               ),
  //             ],
  //           ),
  //         ),
  //       if (selectedProject?.wantsNewarcPictures == true || selectedProject?.wantsNewarcPlanimetry == true)
  //         Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 SizedBox(height: 10),
  //                 Divider(
  //                   thickness: .25,
  //                   color: Color(0xffa6a6a6),
  //                   // indent: 30,
  //                   endIndent: 30,
  //                 ),
  //                 SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     Text(
  //                       'Totale progetto:',
  //                       style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
  //                     ),
  //                     Text(
  //                       '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalCost()! - ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalVAT()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
  //                       style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
  //                     ),
  //                   ],
  //                 ),
  //                 SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     Text(
  //                       'IVA:',
  //                       style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //                     ),
  //                     Text(
  //                       '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalVAT()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
  //                       style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //             SizedBox(height: 10),
  //             Divider(
  //               thickness: .25,
  //               color: Color(0xffa6a6a6),
  //               // indent: 30,
  //               endIndent: 30,
  //             ),
  //             SizedBox(height: 10),
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Text(
  //                   'Totale:',
  //                   style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
  //                 ),
  //                 Text(
  //                   '${(((ImmaginaProjectEconomics(project: selectedProject!).computeNewarcMediaRilieviFeeTotalCost()!) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
  //                   style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
  //                 ),
  //               ],
  //             ),
  //           ],
  //         )
  //     ],
  //   );
  // }

  Column _fullPay() {
    ImmaginaProjectEconomics immaginaProjectEconomics = ImmaginaProjectEconomics(project: selectedProject!);
    bool isDiscounted = selectedProject!.childrenProjects!.length > 1;
    bool isOptionals = selectedProject!.optionals.isNotEmpty;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Completa il servizio',
          style: TextStyle(fontFamily: 'Raleway-800', fontSize: 18, color: Theme.of(context).disabledColor),
        ),
        SizedBox(height: 20),
        Text(
          'Tagli: ${selectedProject!.childrenProjects?.length ?? ""}',
          style: TextStyle(fontFamily: 'Raleway-500', fontSize: 12, color: Colors.grey[500]),
        ),
        ...selectedProject!.childrenProjects?.map((child) {
          int childIndex = selectedProject!.childrenProjects!.indexOf(child);
          int childCost = immaginaProjectEconomics.computeProfessionalsRenderRequestFee(childIndex) ?? 0;
          return Padding(
            padding: EdgeInsets.only(top: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Taglio ${childIndex + 1} - ${child.grossSquareFootage ?? ""} mq',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
                Text(
                  '${(((childCost) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
          );
        }).toList() ?? [],
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10),
            Divider(
              thickness: .25,
              color: Color(0xffa6a6a6),
              // indent: 30,
              endIndent: 30,
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Totale progetto:',
                  style: TextStyle(
                    fontFamily: 'Raleway-700', 
                    fontSize: 14, 
                    color: Theme.of(context).disabledColor,
                  ),
                ),
                Text(
                  '${((immaginaProjectEconomics.computeProfessionalsTotalCost() * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor, decoration: isDiscounted ? TextDecoration.lineThrough : null,),
                ),
              ],
            ),
            if (isDiscounted)
            SizedBox(height: 10),
            if (isDiscounted)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Sconto ${(immaginaProjectEconomics.computeProfessionalsDiscount()['discountPerc']*100).round()}% :',
                  style: TextStyle(
                    fontFamily: 'Raleway-700', 
                    fontSize: 14, 
                    color: Theme.of(context).disabledColor, 
                  ),
                ),
                Text(
                  '${((immaginaProjectEconomics.computeProfessionalsDiscount()['discountAmount'] * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
            if (isDiscounted)
            SizedBox(height: 10),
            if (isDiscounted)
            Divider(
              thickness: .25,
              color: Color(0xffa6a6a6),
              // indent: 30,
              endIndent: 30,
            ),
            if (isDiscounted)
            SizedBox(height: 10),
            if (isDiscounted)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Totale scontato:',
                  style: TextStyle(
                    fontFamily: 'Raleway-700', 
                    fontSize: 14, 
                    color: Theme.of(context).disabledColor, 
                  ),
                ),
                Text(
                  '${((immaginaProjectEconomics.computeProfessionalsTotalCostWithDiscount() * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
            if (isOptionals)
            SizedBox(height: 10),
            if (isOptionals)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Optionals:',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
                Text(
                  '${((immaginaProjectEconomics.computeProfessionalsOptionalsCost() * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'IVA:',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
                Text(
                  '${((immaginaProjectEconomics.computeProfessionalsTotalVAT() * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
                  style: TextStyle(fontFamily: 'Raleway-600', fontSize: 14, color: Theme.of(context).disabledColor),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 10),
        Divider(
          thickness: .25,
          color: Color(0xffa6a6a6),
          // indent: 30,
          endIndent: 30,
        ),
        SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Totale:',
              style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
            ),
            Text(
              '${(((immaginaProjectEconomics.computeProfessionalsTotalCostWithDiscount() + immaginaProjectEconomics.computeProfessionalsOptionalsCost() + immaginaProjectEconomics.computeProfessionalsTotalVAT()) * 100).round() / 100).toStringAsFixed(2).replaceAll('.', ',')}€',
              style: TextStyle(fontFamily: 'Raleway-700', fontSize: 14, color: Theme.of(context).disabledColor),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> updateAgencyAndProject({required String? agencyId, required String? projectId, required bool isPaidWithCredits,required String? immaginaProjectId}) async {
    final FirebaseFirestore firestore = FirebaseFirestore.instance;

    try {
      await firestore.runTransaction((transaction) async {
        // Step 1: Get agency document
        final agencyDocRef = firestore.collection(appConfig.COLLECT_AGENCIES).doc(agencyId);
        final agencySnapshot = await transaction.get(agencyDocRef);

        if (!agencySnapshot.exists) {
          throw Exception("Agency does not exist");
        }

        final data = agencySnapshot.data();
        // int subscriptionServiceCountLeft = data?['subscriptionServiceCountLeft'] ?? 0;

        // // Computing
        // String? agencySubscriptionId = data?['subscriptionId'];
        // String agencySuccessFee = appConst.BASE_NEWARC_IMMAGINA_SUCCESS_FEE;
        // if (isPaidWithCredits) {
        //   final subscriptionDocRef = firestore.collection(appConfig.COLLECT_IMMAGINA_SUBSCRIPTION).doc(agencySubscriptionId);
        //   final subscriptionSnapshot = await transaction.get(subscriptionDocRef);
        //   if (subscriptionSnapshot.exists) {
        //     final subscriptionData = subscriptionSnapshot.data();
        //     agencySuccessFee = subscriptionData!['successFee'];
        //   } else {
        //     throw Exception("Subscription does not exist");
        //   }
        // }
        // print("Success Fee: ${agencySuccessFee}");

        // if (subscriptionServiceCountLeft > 0) {
        //   transaction.update(agencyDocRef, {
        //     'subscriptionServiceCountLeft': subscriptionServiceCountLeft - 1,
        //   });
        // }
        Map<String,dynamic> emailVariable = {
          "agencyname": widget.agencyUser.agency?.name,
          "immaginaprojectid": immaginaProjectId,
        };
        // new request send notification mail
        sendEmail(templateId: CommonUtils.newRequestEmailTemplateId, agency: Agency(data!, agencyId ?? ""), subject: CommonUtils.newRequestEmailSubject);
        final renderistUsers = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', isEqualTo: 'master-renderist')
          .where('isActive', isEqualTo: true)
          .get();
        final masterUsers = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', isEqualTo: 'master')
          .where('isActive', isEqualTo: true)
          .get();
        for (var userDoc in renderistUsers.docs + masterUsers.docs) {
          final userData = userDoc.data();
          if (userData['email'] != null) {
            sendEmail(
              templateId: CommonUtils.agencyNewRequestForWorkSideEmailTemplateId, 
              agency: Agency(data!, agencyId ?? ""), 
              subject: CommonUtils.agencyNewRequestForWorkSideEmailSubject, 
              variables: emailVariable, 
              recipientEmail: userData['email'], 
              recipientName: userData['firstName'] != null ? "${userData['firstName']} ${userData['lastName'] ?? ''}" : "Renderist",
            );
          }
        }
        final projectDocRef = firestore.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(projectId);
        transaction.update(projectDocRef, {
          'requestStatus': 'in analisi',
          'statusChangedDate': DateTime.now().millisecondsSinceEpoch,
          'receivedPaymentDate': DateTime.now().millisecondsSinceEpoch,
          "receivedPayment": true,
          'isPaidWithCredits': isPaidWithCredits,
          'appliedSuccessFee': "0",
        });
      });

      print('Transaction completed successfully');
    } catch (e) {
      print('Transaction failed: $e');
    }
  }

  dynamic confirmationPopup(context) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Center(
              child: Container(
                width: 800,
                child: Material(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  child: Container(
                    width: MediaQuery.of(context).size.width / 1.8,
                    height: MediaQuery.of(context).size.height / 2,
                    padding: EdgeInsets.all(30),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/icons/check.svg',
                          color: Theme.of(context).primaryColor,
                        ),
                        SizedBox(
                          height: 30,
                        ),
                        Text(
                          "Richiesta di progetto inviata!",
                          textAlign: TextAlign.center,
                          style: TextStyle(fontFamily: 'Raleway-700', fontSize: 30, color: Colors.black),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width / 2.5,
                          child: Column(
                            children: [
                              Text(
                                "Il tuo progetto sarà completato entro i prossimi 5 giorni lavorativi, a partire dal prossimo giorno lavorativo. ",
                                textAlign: TextAlign.center,
                                style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Color(0xff4A4A4A)),
                              ),
                              SizedBox(
                                height: 30,
                              ),
                              Text(
                                "In caso di eventuali problemi legati alla progettazione ti contatteremo per la risoluzione. In questo caso potrebbero essere necessari più giorni lavorativi al completamento del progetto.",
                                textAlign: TextAlign.center,
                                style: TextStyle(fontFamily: 'Raleway-500', fontSize: 14, color: Colors.grey[500]),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 50,
                        ),
                        SizedBox(
                          width: 200,
                          child: BaseNewarcButton(
                            buttonText: 'OK',
                            onPressed: () async {
                              Navigator.of(context).pop();
                              widget.onClose();
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ));
        });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 20,
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        child: Stack(
          children: [
            Positioned(
              top: 18,
              right: 20,
              child: Container(
                height: 20,
                width: 20,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                      child: SvgPicture.asset(
                        'assets/icons/close-popup.svg',
                        width: 18,
                        color: Color(0xffb3b3b3),
                      ),
                      onTap: () {
                        if (selectedView == 'initial') {
                          Navigator.of(context).pop();
                        } else if (
                          selectedView == 'pagamento' && 
                          ((selectedProject!.childrenProjects != null && selectedProject!.childrenProjects!.isNotEmpty)
                            ? selectedProject!.childrenProjects!.any((child) => child.grossSquareFootage! >= professionalsGSFUpperLimit)
                            : false)
                        ) {
                          Navigator.of(context).pop();
                          widget.onClose();
                        } else {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return Center(
                                    child: Container(
                                      width: 400,
                                      child: BaseNewarcPopup(
                                        title: 'Vuoi davvero uscire \ndalla procedura?',
                                        noButton: true,
                                        isShowCloseIcon: false,
                                        column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                                          return Column(
                                            mainAxisAlignment: MainAxisAlignment.end,
                                            children: [
                                              ...!_isFrozen ? [BaseNewarcButton(
                                                buttonText: 'Rimani',
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                textColor: Colors.white,
                                                color: Theme.of(context).primaryColor,
                                                disableButton: _isFrozen,
                                              ),
                                              SizedBox(
                                                height: 15,
                                              ),
                                              BaseNewarcButton(
                                                buttonText: 'Salva e chiudi',
                                                onPressed: () async {
                                                  setState(() {
                                                    _isFrozen = true;
                                                    loading = true;
                                                  });
                                                  // save to selectedProject features not saved onChange
                                                  // saveCharacteristics();
                                                  // await savePlanimetryImages();
                                                  // await savePicturesImages();
                                                  // save selectedProject to firestore
                                                  await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
                                                  setState(() {
                                                    _isFrozen = false;
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                  Navigator.of(context).pop();
                                                  widget.onClose();
                                                },
                                                textColor: Theme.of(context).primaryColor,
                                                color: Colors.white,
                                                disableButton: _isFrozen,
                                              )]
                                              : [Center(child: CircularProgressIndicator())]
                                            ],
                                          );
                                        }),
                                      ),
                                    ));
                              });
                        }
                      }),
                ),
              ),
            ),
            Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 20.0, top: 20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20,
                      ),
                      child: NarFormLabelWidget(
                        label: 'Nuova richiesta progetto per professionisti',
                        textColor: Color(0xFF565656),
                        fontSize: 18,
                        fontWeight: '600',
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 40.0,
                        right: 40.0,
                        top: 25,
                      ),
                      child: Container(
                        width: MediaQuery.of(context).size.width / 1.5,
                        height: MediaQuery.of(context).size.height / 1.5,
                        child: selectView(),
                      ),
                    ),
                    SizedBox(height: 10),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        formErrorMessage!.length == 0 || formErrorMessage![0] == ''
                            ? Container()
                            : NarFormLabelWidget(
                          label: formErrorMessage!.length > 0 ? formErrorMessage!.join('') : '',
                          fontSize: 12,
                          fontWeight: 'bold',
                        ),
                      ],
                    ),
                    SizedBox(height: 5),
                    Container(width: MediaQuery.of(context).size.width / 1.5, height: MediaQuery.of(context).size.height / 10, child: Center(child: selectFooter())),
                  ],
                ),
              ),
            ),
            if (_isFrozen)
              Positioned.fill(
                child: Container(
                  color: Colors.black54,
                  child: Center(
                    child: !loading ? CircularProgressIndicator() : null,
                  ), // Semi-transparent overlay
                ),
              ),
          ],
        ),
      ),
    );
  }
}

Future<void> deleteArchivedProject(String projectId) async {
    try {
      await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(projectId).update({'isArchived': true});
      print("Project ${projectId} successfully marked as archived.");
    } catch (e) {
      print("Error archiving project $projectId: $e");
    }
  }
